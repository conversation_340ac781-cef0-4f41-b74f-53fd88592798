'use client';
import React from 'react';
import { LOADING_MESSAGES } from '../../_utils/diaryConstants';

/**
 * Reusable loading state component for diary pages
 * @param {Object} props - Component props
 * @param {string} props.message - Loading message to display
 * @param {string} props.variant - Loading variant ('default', 'compact', 'inline')
 * @param {string} props.className - Additional CSS classes
 */
const DiaryLoadingState = ({ 
  message = LOADING_MESSAGES.diary, 
  variant = 'default',
  className = ''
}) => {
  const getContainerClasses = () => {
    const baseClasses = ['flex items-center justify-center'];
    
    switch (variant) {
      case 'compact':
        return `${baseClasses.join(' ')} h-32`;
      case 'inline':
        return `${baseClasses.join(' ')} py-4`;
      default:
        return `${baseClasses.join(' ')} h-[500px]`;
    }
  };

  const getSpinnerClasses = () => {
    switch (variant) {
      case 'compact':
        return 'h-8 w-8 border-t-2 border-b-2 border-yellow-400 mb-2';
      case 'inline':
        return 'h-6 w-6 border-t-2 border-b-2 border-yellow-400 mb-2';
      default:
        return 'h-12 w-12 border-t-2 border-b-2 border-yellow-400 mb-4';
    }
  };

  const getTextClasses = () => {
    switch (variant) {
      case 'compact':
        return 'text-lg font-medium';
      case 'inline':
        return 'text-sm font-medium';
      default:
        return 'text-xl font-semibold';
    }
  };

  return (
    <div className={`${getContainerClasses()} ${className}`}>
      <div className="text-center">
        <div className={`inline-block animate-spin rounded-full ${getSpinnerClasses()}`}></div>
        <h2 className={getTextClasses()}>{message}</h2>
      </div>
    </div>
  );
};

export default DiaryLoadingState;
