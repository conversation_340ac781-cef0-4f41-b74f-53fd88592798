'use client';

import { useState, useMemo, useCallback, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { ButtonIcon } from '@/components/Button';
import useDataFetch from '@/hooks/useDataFetch';

// Components
import DiaryCover from '../_component/DiaryCover';
import DiaryContent from '../_component/DiaryContent';
import DiaryPagination from '../_component/DiaryPagination';
import DiaryIconsSidebar from '../_component/DiaryIconsSidebar';

// Shared utilities and hooks
import { DiaryConfigProvider } from '../_config/diaryProvider';
import DiaryContainer from '../_components/shared/DiaryContainer';
import DiaryCalendarButton from '../_components/shared/DiaryCalendarButton';
import DiaryLoadingState from '../_components/shared/DiaryLoadingState';
import DiaryErrorState from '../_components/shared/DiaryErrorState';
import { PAGINATION_DEFAULTS, LOADING_MESSAGES } from '../_utils/diaryConstants';

// Redux
import {
  setMyDiaryIsOpen,
  setMyDiaryCurrentIndex,
  setMyDiaryCurrentPage,
  setMyDiaryCoverPhotoUrl,
  resetMyDiaryState,
  selectMyDiaryIsOpen,
  selectMyDiaryCurrentIndex,
  selectMyDiaryCurrentPage,
  selectMyDiaryCoverPhotoUrl,
} from '@/store/features/commonSlice';

function MyDiaryContent() {
  const dispatch = useDispatch();
  
  // Redux state
  const isOpen = useSelector(selectMyDiaryIsOpen);
  const currentIndex = useSelector(selectMyDiaryCurrentIndex);
  const currentPage = useSelector(selectMyDiaryCurrentPage);
  const coverPhotoUrl = useSelector(selectMyDiaryCoverPhotoUrl);

  // Local state
  const [showSideIcons, setShowSideIcons] = useState(false);

  // Fetch diary entries
  const {
    data: diaryData,
    isLoading,
    error,
    refetch: fetchEntries
  } = useDataFetch({
    queryKey: ['my-diary-entries', currentPage],
    endPoint: '/diary/entries/my',
    params: {
      page: currentPage,
      limit: PAGINATION_DEFAULTS.itemsPerPage,
      sortBy: 'entryDate',
      sortDirection: 'DESC'
    }
  });

  // Computed values
  const entries = diaryData?.items || [];
  const paginationMeta = diaryData?.meta || {};
  const hasEntries = entries.length > 0;
  const isMobile = typeof window !== 'undefined' && window.innerWidth < 768;
  const isSinglePage = !isMobile && entries.length === 1;

  // Background style
  const backgroundStyle = useMemo(() => {
    if (!isOpen && coverPhotoUrl) {
      return {
        backgroundImage: `url(${coverPhotoUrl})`,
        backgroundSize: 'cover',
        backgroundPosition: 'center',
        backgroundRepeat: 'no-repeat'
      };
    }
    return {};
  }, [isOpen, coverPhotoUrl]);

  // Handle responsive sidebar behavior
  useEffect(() => {
    const handleResize = () => {
      const isDesktop = window.innerWidth >= 1280; // xl breakpoint
      if (isDesktop) {
        setShowSideIcons(true);
      } else {
        setShowSideIcons(false);
      }
    };

    handleResize();
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Navigation functions
  const goLeft = useCallback(() => {
    const step = isMobile ? 1 : 2;
    const newIndex = Math.max(0, currentIndex - step);
    
    if (newIndex < currentIndex) {
      dispatch(setMyDiaryCurrentIndex(newIndex));
    } else if (currentPage > 1) {
      // Go to previous page
      const newPage = currentPage - 1;
      dispatch(setMyDiaryCurrentPage(newPage));
      dispatch(setMyDiaryCurrentIndex(0));
    }
  }, [dispatch, currentIndex, currentPage, isMobile]);

  const goRight = useCallback(() => {
    const step = isMobile ? 1 : 2;
    const maxIndexForPage = Math.min(
      PAGINATION_DEFAULTS.itemsPerPage - step,
      entries.length - step
    );
    
    if (currentIndex + step < entries.length) {
      dispatch(setMyDiaryCurrentIndex(currentIndex + step));
    } else if (currentPage < paginationMeta.totalPages) {
      // Go to next page
      const newPage = currentPage + 1;
      dispatch(setMyDiaryCurrentPage(newPage));
      dispatch(setMyDiaryCurrentIndex(0));
    }
  }, [dispatch, currentIndex, currentPage, entries.length, paginationMeta.totalPages, isMobile]);

  // Diary operations
  const openDiary = useCallback(() => {
    dispatch(setMyDiaryIsOpen(true));
  }, [dispatch]);

  const closeDiary = useCallback(() => {
    dispatch(setMyDiaryIsOpen(false));
  }, [dispatch]);

  const handleDateSelect = useCallback(() => {
    fetchEntries();
  }, [fetchEntries]);

  // Reset state on unmount
  useEffect(() => {
    return () => {
      dispatch(resetMyDiaryState());
    };
  }, [dispatch]);

  // Error state
  if (error) {
    return (
      <div className="flex justify-center min-h-[calc(100vh-180px)] p-4">
        <DiaryContainer variant="my-diary" isOpen={isOpen}>
          <DiaryErrorState
            message="Failed to load your diary entries"
            onRetry={fetchEntries}
          />
        </DiaryContainer>
      </div>
    );
  }

  // Loading state
  if (isLoading) {
    return (
      <div className="flex justify-center min-h-[calc(100vh-180px)] p-4">
        <DiaryContainer variant="my-diary" isOpen={isOpen}>
          <DiaryLoadingState message={LOADING_MESSAGES.entries} />
        </DiaryContainer>
      </div>
    );
  }

  return (
    <div className="flex justify-center min-h-[calc(100vh-180px)] p-4">
      <DiaryContainer
        variant="my-diary"
        isOpen={isOpen}
        style={backgroundStyle}
        className={`${isSinglePage ? 'min-h-[600px] max-h-[calc(100vh-180px)]' : 'min-h-[600px] max-h-[calc(100vh-180px)]'} ${
          !isOpen && coverPhotoUrl ? '' : 'bg-[#FDE7E9]'
        } rounded-lg shadow-lg border bg-yellow-100 border-gray-300 transition-all duration-300`}
      >
        {isOpen ? (
          <>
            <DiaryContent
              entries={entries}
              currentIndex={currentIndex}
              isMobile={isMobile}
            />
            
            {/* Header with close button and calendar */}
            <div className="absolute lg:right-[-60px] right-0 -top-14 lg:top-4 z-10 flex flex-col max-sm:flex-row-reverse items-center">
              <ButtonIcon
                icon="mdi:close"
                innerBtnCls="h-10 w-10 cursor-pointer"
                btnIconCls="h-5 w-5"
                aria-label="close diary"
                onClick={closeDiary}
              />
              
              <div className="mt-2 max-sm:mt-0 max-sm:mr-2">
                <DiaryCalendarButton
                  onDateSelect={handleDateSelect}
                  fetchDiaryEntry={fetchEntries}
                />
              </div>
            </div>

            {/* Side Icons */}
            {showSideIcons && (
              <div className="absolute left-1/2 -translate-x-1/2 max-sm:-top-8 top-0 bg-gradient-to-b from-yellow-300 via-yellow-400 to-yellow-500 px-4 pr-8 py-1 rounded-full">
                <DiaryIconsSidebar
                  isOpen={showSideIcons}
                  onClose={() => setShowSideIcons(false)}
                  isEmojiSelectorOpen={false}
                />
              </div>
            )}
          </>
        ) : (
          <DiaryCover
            hasEntries={hasEntries}
            onOpen={openDiary}
            onDateSelect={handleDateSelect}
            fetchTodayEntry={fetchEntries}
          />
        )}

        {/* Pagination */}
        <DiaryPagination
          hasEntries={hasEntries}
          isOpen={isOpen}
          currentIndex={currentIndex}
          totalEntries={paginationMeta?.totalItems}
          currentPage={paginationMeta?.currentPage}
          totalPages={paginationMeta?.totalPages}
          itemsPerPage={paginationMeta?.itemsPerPage}
          isMobile={isMobile}
          onLeftClick={goLeft}
          onRightClick={goRight}
        />
      </DiaryContainer>
    </div>
  );
}

export default function MyDiary() {
  return (
    <DiaryConfigProvider pageType="myDiary">
      <MyDiaryContent />
    </DiaryConfigProvider>
  );
}
