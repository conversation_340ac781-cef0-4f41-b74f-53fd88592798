'use client';
import React, { createContext, useContext } from 'react';
import { useDiarySkinContext } from '../../_context/DiaryContext';
import StageSelector from '../StageSelector';
import DiaryWithDecorations from '@/components/diary/DiaryWithDecorations';
import DecorationUndoRedo from '@/components/diary/DecorationUndoRedo';

/**
 * Compound component pattern for diary skin
 * Reduces prop drilling by using context internally
 */

// Skin context for compound components
const DiarySkinCompoundContext = createContext(null);

/**
 * Main diary skin compound component
 */
const DiarySkinCompound = ({ children, className = '', ...props }) => {
  const skinContext = useDiarySkinContext();
  
  return (
    <DiarySkinCompoundContext.Provider value={skinContext}>
      <div className={`diary-skin-compound bg-white h-full p-2 shadow-xl ${className}`} {...props}>
        {children}
      </div>
    </DiarySkinCompoundContext.Provider>
  );
};

/**
 * Hook to use skin compound context
 */
const useSkinCompoundContext = () => {
  const context = useContext(DiarySkinCompoundContext);
  if (!context) {
    throw new Error('Skin compound components must be used within DiarySkinCompound');
  }
  return context;
};

/**
 * Stage selector compound component
 */
const StageSelector = ({ className = '', ...props }) => {
  const { selectedStageTemplateId, handleStageChange } = useSkinCompoundContext();
  
  return (
    <div className={`diary-skin-stage-selector ${className}`}>
      <StageSelector
        onStageChange={handleStageChange}
        selectedTemplateId={selectedStageTemplateId}
        {...props}
      />
    </div>
  );
};

/**
 * Score display compound component
 */
const Score = ({ className = '', ...props }) => {
  const { diaryEntry } = useSkinCompoundContext();
  
  if (diaryEntry?.status !== 'reviewed' || !diaryEntry?.score) {
    return null;
  }
  
  return (
    <div 
      className={`text-sm text-[#864D0D] font-medium py-2 px-3 max-sm:py-1 max-sm:px-2 border border-dashed border-[#ECB306] bg-[#FFF189] rounded-md ${className}`}
      {...props}
    >
      score: {diaryEntry.score}
    </div>
  );
};

/**
 * Decoration controls compound component
 */
const DecorationControls = ({ className = '', ...props }) => {
  const { isDecorating } = useSkinCompoundContext();
  
  if (!isDecorating) return null;
  
  return (
    <div className={`diary-skin-decoration-controls flex justify-center my-2 ${className}`} {...props}>
      <DecorationUndoRedo />
    </div>
  );
};

/**
 * Skin preview compound component
 */
const Preview = ({ className = '', ...props }) => {
  const { 
    selectedSkin, 
    diaryEntry, 
    currentEntryDate,
    isDecorating,
    submitDecorations 
  } = useSkinCompoundContext();
  
  return (
    <div className={`diary-skin-preview ${className}`} {...props}>
      <DiaryWithDecorations
        today={currentEntryDate}
        todayEntry={diaryEntry}
        selectedSkin={selectedSkin}
        onSubmitDecoration={submitDecorations}
        isDecorating={isDecorating}
      />
    </div>
  );
};

/**
 * Header compound component
 */
const Header = ({ children, className = '', ...props }) => {
  return (
    <div className={`diary-skin-header mb-4 flex items-center justify-between ${className}`} {...props}>
      {children}
    </div>
  );
};

/**
 * Actions container compound component
 */
const Actions = ({ children, className = '', ...props }) => {
  return (
    <div className={`diary-skin-actions flex items-center gap-3 ${className}`} {...props}>
      {children}
    </div>
  );
};

/**
 * Decoration status compound component
 */
const DecorationStatus = ({ className = '', ...props }) => {
  const { 
    isDecorating, 
    isSubmittingDecoration, 
    decorationSubmissionError,
    decorationSubmissionSuccess 
  } = useSkinCompoundContext();
  
  if (!isDecorating) return null;
  
  let statusText = '';
  let statusClass = '';
  
  if (isSubmittingDecoration) {
    statusText = 'Saving decorations...';
    statusClass = 'text-blue-600';
  } else if (decorationSubmissionError) {
    statusText = 'Failed to save decorations';
    statusClass = 'text-red-600';
  } else if (decorationSubmissionSuccess) {
    statusText = 'Decorations saved successfully';
    statusClass = 'text-green-600';
  }
  
  if (!statusText) return null;
  
  return (
    <div className={`diary-skin-decoration-status text-sm ${statusClass} ${className}`} {...props}>
      {statusText}
    </div>
  );
};

/**
 * Skin info compound component
 */
const SkinInfo = ({ className = '', showName = true, showDescription = false, ...props }) => {
  const { selectedSkin } = useSkinCompoundContext();
  
  if (!selectedSkin) return null;
  
  return (
    <div className={`diary-skin-info ${className}`} {...props}>
      {showName && (
        <h3 className="font-medium text-gray-800">{selectedSkin.name}</h3>
      )}
      {showDescription && selectedSkin.description && (
        <p className="text-sm text-gray-600 mt-1">{selectedSkin.description}</p>
      )}
    </div>
  );
};

// Attach compound components
DiarySkinCompound.StageSelector = StageSelector;
DiarySkinCompound.Score = Score;
DiarySkinCompound.DecorationControls = DecorationControls;
DiarySkinCompound.Preview = Preview;
DiarySkinCompound.Header = Header;
DiarySkinCompound.Actions = Actions;
DiarySkinCompound.DecorationStatus = DecorationStatus;
DiarySkinCompound.SkinInfo = SkinInfo;

export default DiarySkinCompound;
