'use client';
import React from 'react';
import { DiaryConfigProvider } from '../_config/diaryProvider';
import { DiaryContextProvider } from '../_context/DiaryContext';
import DiaryLayout from '../_components/shared/DiaryLayout';
import DiaryFormCompound from '../_components/compound/DiaryFormCompound';
import DiarySkinCompound from '../_components/compound/DiarySkinCompound';

/**
 * Example of how to use the optimized diary components
 * This demonstrates the reduced prop drilling and cleaner component structure
 */

function OptimizedDiaryContent({ entryId = null, date }) {
  return (
    <DiaryContextProvider entryId={entryId} date={date}>
      <DiaryLayout
        variant="main"
        isOpen={true}
        showSideIcons={true}
      >
        <div className="grid grid-cols-1 md:grid-cols-2 gap-2 items-center relative">
          
          {/* Diary Skin with Compound Components */}
          <DiarySkinCompound>
            <DiarySkinCompound.Header>
              <DiarySkinCompound.StageSelector />
              <DiarySkinCompound.Actions>
                <DiarySkinCompound.Score />
                <DiarySkinCompound.DecorationControls />
              </DiarySkinCompound.Actions>
            </DiarySkinCompound.Header>
            
            <DiarySkinCompound.Preview />
            <DiarySkinCompound.DecorationStatus />
          </DiarySkinCompound>

          {/* Diary Form with Compound Components */}
          <div className="bg-white h-full p-2 overflow-hidden shadow-xl">
            <DiaryFormCompound>
              <div className="flex flex-col h-full">
                
                {/* Form Header */}
                <div className="mb-4">
                  <DiaryFormCompound.Subject 
                    placeholder="What's your diary about today?"
                    className="mb-2"
                  />
                  <div className="flex items-center justify-between">
                    <DiaryFormCompound.WordCount showLabel={true} />
                    <DiaryFormCompound.Status />
                  </div>
                </div>

                {/* Form Content */}
                <div className="flex-grow mb-4">
                  <DiaryFormCompound.Message 
                    placeholder="Write your thoughts here..."
                    rows={12}
                  />
                </div>

                {/* Form Actions */}
                <DiaryFormCompound.Actions className="justify-between">
                  <DiaryFormCompound.EditToggle />
                  <DiaryFormCompound.SaveButton variant="primary">
                    Save & Continue
                  </DiaryFormCompound.SaveButton>
                </DiaryFormCompound.Actions>
                
              </div>
            </DiaryFormCompound>
          </div>
        </div>
      </DiaryLayout>
    </DiaryContextProvider>
  );
}

/**
 * Main optimized diary example component
 */
export default function OptimizedDiaryExample({ entryId, date }) {
  return (
    <DiaryConfigProvider pageType="main">
      <OptimizedDiaryContent entryId={entryId} date={date} />
    </DiaryConfigProvider>
  );
}

/**
 * Example usage in different scenarios:
 * 
 * // For new diary entry
 * <OptimizedDiaryExample date="2024-01-15" />
 * 
 * // For editing existing entry
 * <OptimizedDiaryExample entryId="123" date="2024-01-15" />
 * 
 * // The compound components eliminate the need to pass props down multiple levels:
 * 
 * // Before optimization (lots of prop drilling):
 * <DiaryForm
 *   subject={subject}
 *   message={message}
 *   wordCount={wordCount}
 *   validationErrors={validationErrors}
 *   isEdit={isEdit}
 *   isSaving={isSaving}
 *   isEditable={isEditable}
 *   handleSubjectChange={handleSubjectChange}
 *   handleMessageChange={handleMessageChange}
 *   setIsEdit={setIsEdit}
 *   handleSave={handleSave}
 *   // ... many more props
 * />
 * 
 * // After optimization (no prop drilling):
 * <DiaryFormCompound>
 *   <DiaryFormCompound.Subject />
 *   <DiaryFormCompound.Message />
 *   <DiaryFormCompound.WordCount />
 *   <DiaryFormCompound.SaveButton />
 * </DiaryFormCompound>
 * 
 * Benefits:
 * 1. Reduced prop drilling - context handles state distribution
 * 2. Cleaner component APIs - each compound component has focused responsibility
 * 3. Better reusability - components can be composed differently
 * 4. Easier testing - each compound component can be tested in isolation
 * 5. Better maintainability - changes to state management don't require prop updates
 * 6. Type safety - context provides better TypeScript support
 * 7. Performance - memoization at context level reduces unnecessary re-renders
 */
