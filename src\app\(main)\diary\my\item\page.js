'use client';

import { useEffect, useCallback, useState, useRef } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useDispatch, useSelector } from 'react-redux';

import DiaryForm from '../../_component/DiaryForm';
import DiarySkin from '../../_component/DiarySkin';
import SelectSkinModal from '../../_component/SelectSkinModal';
import MessageModal from '../../_component/modalContents/MessageModal';

// Shared utilities and hooks
import { useDiaryEntry } from '../../_hooks/useDiaryEntry';
import { useDiaryState } from '../../_hooks/useDiaryState';
import { useDiaryStage } from '../../_hooks/useDiaryStage';
import { useDiaryDecoration } from '../../_hooks/useDiaryDecoration';
import { DiaryConfigProvider } from '../../_config/diaryProvider';
import DiaryLayout from '../../_components/shared/DiaryLayout';
import DiaryCalendarButton from '../../_components/shared/DiaryCalendarButton';
import { getCurrentEntryDate } from '../../_utils/diaryHelpers';

// Redux imports
import {
  setPreviewMode,
  updateCanvasItem,
  changeBackground,
  addImageToCanvas,
  resetCanvas,
  selectCanvasItems,
  selectCanvasBackground,
  setSelectedId,
} from '@/store/features/canvasSlice';

import { selectDiaryContainerProps } from '../../_store/diarySelectors';

function WriteDiaryItemContent() {
  const router = useRouter();
  const dispatch = useDispatch();
  const searchParams = useSearchParams();
  const isInitialMount = useRef(true);
  
  // Get entryId from URL parameters
  const entryId = searchParams.get('entryId');
  const dateParam = searchParams.get('date');
  
  // Use custom hooks for diary functionality
  const { date } = useSelector((state) => state.diary);
  const { user } = useSelector((state) => state.auth);
  const canvasItems = useSelector(selectCanvasItems);
  const containerProps = useSelector(selectDiaryContainerProps);
  
  // Use the date from URL params or fallback to Redux state
  const currentDate = dateParam || date;
  
  // Diary state management
  const {
    subject,
    message,
    wordCount,
    isEdit,
    setIsEdit,
    selectedSkin,
    isSaving,
    isLoading,
    todayEntry,
    isSkinModalOpen,
    layoutBackground,
    handleSubjectChange,
    handleMessageChange,
    hasUnsavedChanges
  } = useDiaryState();

  // Diary entry operations
  const {
    diaryEntry,
    validationErrors,
    selectedStageTemplateId,
    setSelectedStageTemplateId,
    fetchDiaryEntry,
    saveDiaryEntry
  } = useDiaryEntry(entryId, currentDate);

  // Stage management
  const {
    selectedStage,
    nextStage,
    handleStageChange,
    fetchDiarySettings
  } = useDiaryStage(selectedStageTemplateId);

  // Decoration management
  const {
    isDecorating,
    decorationItems,
    toggleDecorationMode,
    submitDecorations,
    clearAllDecorations
  } = useDiaryDecoration();

  // Local state
  const [hasTutorGreeting, setHasTutorGreeting] = useState(false);
  const [showSideIcons, setShowSideIcons] = useState(false);
  const [showCover, setShowCover] = useState(true);

  // Computed values
  const currentEntryDate = getCurrentEntryDate(diaryEntry, currentDate);
  const isOpen = !showCover;

  // Handle responsive sidebar behavior
  useEffect(() => {
    const handleResize = () => {
      const isDesktop = window.innerWidth >= 1280; // xl breakpoint
      if (isDesktop) {
        setShowSideIcons(true); // Always show sidebar on desktop
      } else {
        setShowSideIcons(false); // Hide sidebar on mobile/tablet by default
      }
    };

    // Initial check
    handleResize();

    // Add event listener
    window.addEventListener('resize', handleResize);
    // Cleanup
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Initialize diary settings on mount
  useEffect(() => {
    if (isInitialMount.current) {
      fetchDiarySettings();
      setShowCover(false); // Auto-open for item view
      isInitialMount.current = false;
    }
  }, [fetchDiarySettings]);

  // Handle save operation
  const handleSave = useCallback(async () => {
    const success = await saveDiaryEntry();
    if (success && nextStage) {
      // Auto-progress to next stage if available
      handleStageChange(nextStage);
    }
  }, [saveDiaryEntry, nextStage, handleStageChange]);

  // Handle decoration submission
  const handleSubmitDecoration = useCallback(async () => {
    if (diaryEntry?.id) {
      await submitDecorations(diaryEntry.id);
    }
  }, [submitDecorations, diaryEntry]);

  // Handle navigation back to my diary
  const handleClose = useCallback(() => {
    router.push('/diary/my');
  }, [router]);

  // Handle date selection from calendar
  const handleDateSelect = useCallback(() => {
    // Calendar component will handle the navigation
    fetchDiaryEntry();
  }, [fetchDiaryEntry]);

  // Render cover when diary is closed
  if (!isOpen) {
    return (
      <div className="flex justify-center items-center min-h-[calc(100vh-180px)] p-6">
        <div className="relative w-full max-w-[647px] min-h-[600px] bg-gradient-to-br from-pink-100 to-yellow-100 rounded-lg shadow-lg border border-gray-300">
          
          {/* Calendar button for cover view */}
          <DiaryCalendarButton
            variant="cover"
            onDateSelect={handleDateSelect}
            fetchDiaryEntry={fetchDiaryEntry}
            className="absolute top-4 right-4"
          />

          <div 
            className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 bg-white/30 backdrop-blur-lg rounded-2xl shadow-xl px-12 py-6 text-center min-w-[200px] border border-white/40 ring-1 ring-black/5 cursor-pointer"
            onClick={() => setShowCover(false)}
          >
            <h1 className="text-2xl font-bold text-[#1E3A8A]">Diary Entry</h1>
            <p className="text-[#3B82F6] mt-1">Click to open</p>
          </div>
        </div>
      </div>
    );
  }

  // Render main diary content
  return (
    <DiaryLayout
      isOpen={isOpen}
      isLoading={isLoading}
      isDecorating={isDecorating}
      onClose={handleClose}
      showSideIcons={showSideIcons}
      onCloseSideIcons={() => setShowSideIcons(false)}
      variant="item"
      headerContent={
        <DiaryCalendarButton
          onDateSelect={handleDateSelect}
          fetchDiaryEntry={fetchDiaryEntry}
        />
      }
    >
      <div className="grid grid-cols-1 md:grid-cols-2 gap-2 items-center relative">
        <DiarySkin
          today={currentEntryDate}
          todayEntry={diaryEntry}
          subject={subject}
          message={message}
          selectedSkin={selectedSkin}
          handleStageChange={handleStageChange}
          selectedStageTemplateId={selectedStageTemplateId}
          fetchTodayEntry={fetchDiaryEntry}
          onSubmitDecoration={handleSubmitDecoration}
          isDecorating={isDecorating}
        />

        <div className="bg-white h-full p-2 overflow-hidden shadow-xl">
          <DiaryForm
            today={currentEntryDate}
            todayEntry={diaryEntry}
            subject={subject}
            message={message}
            wordCount={wordCount}
            selectedStage={selectedStage}
            validationErrors={validationErrors}
            fetchTodayEntry={fetchDiaryEntry}
            nextStage={nextStage}
            handleStageChange={handleStageChange}
            isEditable={isEdit}
            setIsEdit={setIsEdit}
            isEdit={isEdit}
            handleSave={handleSave}
            isSaving={isSaving}
            setIsMessageModalOpen={setHasTutorGreeting}
          />
        </div>
      </div>

      {/* Modals */}
      <SelectSkinModal />
      <MessageModal
        isOpen={hasTutorGreeting}
        onClose={() => setHasTutorGreeting(false)}
      />
    </DiaryLayout>
  );
}

export default function WriteDiaryItem() {
  return (
    <DiaryConfigProvider pageType="item">
      <WriteDiaryItemContent />
    </DiaryConfigProvider>
  );
}
