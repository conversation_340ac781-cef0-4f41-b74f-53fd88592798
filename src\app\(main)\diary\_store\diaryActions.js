import { createAction } from '@reduxjs/toolkit';

/**
 * Enhanced action creators for diary functionality
 * These provide more semantic and reusable actions
 */

// Form actions
export const updateDiaryForm = createAction('diary/updateForm', (formData) => ({
  payload: formData
}));

export const resetDiaryForm = createAction('diary/resetForm');

export const initializeDiaryForm = createAction('diary/initializeForm', (entryData) => ({
  payload: {
    subject: entryData?.title || '',
    message: entryData?.content || '',
    selectedSkin: entryData?.skin || null
  }
}));

// Entry actions
export const startDiaryEntry = createAction('diary/startEntry', (date) => ({
  payload: { date }
}));

export const saveDiaryEntryStart = createAction('diary/saveDiaryEntryStart');
export const saveDiaryEntrySuccess = createAction('diary/saveDiaryEntrySuccess', (entry) => ({
  payload: entry
}));
export const saveDiaryEntryFailure = createAction('diary/saveDiaryEntryFailure', (error) => ({
  payload: error
}));

// UI actions
export const toggleDiaryModal = createAction('diary/toggleModal', (modalType) => ({
  payload: modalType
}));

export const setDiaryLoadingState = createAction('diary/setLoadingState', (loadingStates) => ({
  payload: loadingStates
}));

export const openDiaryWithEntry = createAction('diary/openWithEntry', (entry) => ({
  payload: entry
}));

export const closeDiary = createAction('diary/close');

// Decoration actions
export const startDecorationMode = createAction('diary/startDecorationMode');
export const stopDecorationMode = createAction('diary/stopDecorationMode');

export const saveDecorationState = createAction('diary/saveDecorationState', (decorations) => ({
  payload: decorations
}));

export const submitDecorationsStart = createAction('diary/submitDecorationsStart');
export const submitDecorationsSuccess = createAction('diary/submitDecorationsSuccess');
export const submitDecorationsFailure = createAction('diary/submitDecorationsFailure', (error) => ({
  payload: error
}));

// Stage actions
export const progressToNextStage = createAction('diary/progressToNextStage', (nextStage) => ({
  payload: nextStage
}));

export const setStageWithProgression = createAction('diary/setStageWithProgression', (stage, nextStage) => ({
  payload: { stage, nextStage }
}));

// Batch actions for common operations
export const initializeDiaryPage = createAction('diary/initializePage', (pageData) => ({
  payload: {
    entry: pageData.entry,
    skin: pageData.skin,
    stage: pageData.stage,
    isOpen: pageData.isOpen || false
  }
}));

export const resetDiaryPage = createAction('diary/resetPage');

// My Diary specific actions
export const initializeMyDiary = createAction('diary/initializeMyDiary', (diaryData) => ({
  payload: {
    entries: diaryData.entries,
    currentIndex: diaryData.currentIndex || 0,
    currentPage: diaryData.currentPage || 1,
    coverPhotoUrl: diaryData.coverPhotoUrl || null
  }
}));

export const navigateMyDiary = createAction('diary/navigateMyDiary', (direction) => ({
  payload: direction // 'left', 'right', 'first', 'last'
}));

export const setMyDiaryPage = createAction('diary/setMyDiaryPage', (pageNumber) => ({
  payload: pageNumber
}));

// Error handling actions
export const clearDiaryErrors = createAction('diary/clearErrors');

export const setDiaryError = createAction('diary/setError', (error) => ({
  payload: {
    message: error.message,
    type: error.type || 'general',
    timestamp: Date.now()
  }
}));

// Validation actions
export const setValidationErrors = createAction('diary/setValidationErrors', (errors) => ({
  payload: errors
}));

export const clearValidationErrors = createAction('diary/clearValidationErrors');

// Utility action creators
export const createDiaryAction = (type, payloadCreator) => createAction(type, payloadCreator);

// Action creators for common patterns
export const withLoading = (actionCreator) => (payload) => (dispatch) => {
  dispatch(setDiaryLoadingState({ isLoading: true }));
  const result = dispatch(actionCreator(payload));
  dispatch(setDiaryLoadingState({ isLoading: false }));
  return result;
};

export const withErrorHandling = (actionCreator) => (payload) => async (dispatch) => {
  try {
    dispatch(clearDiaryErrors());
    return await dispatch(actionCreator(payload));
  } catch (error) {
    dispatch(setDiaryError(error));
    throw error;
  }
};
