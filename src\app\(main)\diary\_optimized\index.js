/**
 * Optimized Diary Components Export Index
 * 
 * This file provides a centralized export for all optimized diary components,
 * utilities, hooks, and configurations. Import from this file to use the
 * optimized diary system.
 */

// Utilities
export { 
  diaryValidationSchema, 
  validateDiaryForm 
} from '../_utils/diaryValidation';

export { 
  formatDateToYMD,
  countWords,
  isDiaryEntryEditable,
  getCurrentEntryDate,
  getNextStage,
  createDefaultEntryParams,
  createLoadingState
} from '../_utils/diaryHelpers';

export {
  DIARY_STATUS,
  DEFAULT_DIARY_SETTINGS,
  DIARY_CONTAINER_CLASSES,
  DIARY_LAYOUT_CLASSES,
  LOADING_MESSAGES,
  VALIDATION_MESSAGES,
  DIARY_ENDPOINTS,
  PAGINATION_DEFAULTS
} from '../_utils/diaryConstants';

// Hooks
export { useDiaryEntry } from '../_hooks/useDiaryEntry';
export { useDiaryState } from '../_hooks/useDiaryState';
export { useDiaryStage } from '../_hooks/useDiaryStage';
export { useDiaryDecoration } from '../_hooks/useDiaryDecoration';

// Redux Optimizations
export {
  selectDiaryFormData,
  selectDiaryUIState,
  selectDiaryEntryState,
  selectDecorationState,
  selectMyDiaryState,
  selectCanvasState,
  selectDiaryEditableState,
  selectDiaryLoadingState,
  selectDiaryFormProps,
  selectDiarySkinProps,
  selectDiaryContainerProps
} from '../_store/diarySelectors';

export {
  updateDiaryForm,
  resetDiaryForm,
  initializeDiaryForm,
  startDiaryEntry,
  saveDiaryEntryStart,
  saveDiaryEntrySuccess,
  saveDiaryEntryFailure,
  toggleDiaryModal,
  setDiaryLoadingState,
  openDiaryWithEntry,
  closeDiary,
  startDecorationMode,
  stopDecorationMode,
  saveDecorationState,
  submitDecorationsStart,
  submitDecorationsSuccess,
  submitDecorationsFailure,
  progressToNextStage,
  setStageWithProgression,
  initializeDiaryPage,
  resetDiaryPage,
  initializeMyDiary,
  navigateMyDiary,
  setMyDiaryPage,
  clearDiaryErrors,
  setDiaryError,
  setValidationErrors,
  clearValidationErrors,
  createDiaryAction,
  withLoading,
  withErrorHandling
} from '../_store/diaryActions';

// Configuration
export {
  DIARY_PAGE_CONFIGS,
  STAGE_CONFIG,
  DECORATION_CONFIG,
  VALIDATION_CONFIG,
  API_CONFIG,
  UI_CONFIG,
  FEATURE_FLAGS,
  PERFORMANCE_CONFIG,
  getDiaryPageConfig,
  isFeatureEnabled,
  getApiEndpoint,
  getValidationRules,
  getDecorationLimit
} from '../_config/diaryConfig';

export {
  DiaryConfigProvider,
  useDiaryConfig,
  usePageConfig,
  useFeature,
  useApiConfig,
  useValidationConfig,
  useDecorationConfig
} from '../_config/diaryProvider';

// Context
export {
  DiaryContextProvider,
  useDiaryContext,
  useDiaryFormContext,
  useDiarySkinContext,
  useDiaryDecorationContext,
  useDiaryStageContext
} from '../_context/DiaryContext';

// Shared Components
export { default as DiaryContainer } from '../_components/shared/DiaryContainer';
export { default as DiaryLoadingState } from '../_components/shared/DiaryLoadingState';
export { default as DiaryErrorState } from '../_components/shared/DiaryErrorState';
export { default as DiaryHeader } from '../_components/shared/DiaryHeader';
export { default as DiaryCalendarButton } from '../_components/shared/DiaryCalendarButton';
export { default as DiaryLayout } from '../_components/shared/DiaryLayout';

// Compound Components
export { default as DiaryFormCompound } from '../_components/compound/DiaryFormCompound';
export { default as DiarySkinCompound } from '../_components/compound/DiarySkinCompound';

// Example Usage
export { default as OptimizedDiaryExample } from '../_examples/OptimizedDiaryExample';

/**
 * Quick Start Guide:
 * 
 * 1. Basic Diary Page Setup:
 * ```jsx
 * import { DiaryConfigProvider, DiaryContextProvider, DiaryLayout } from './diary/_optimized';
 * 
 * function MyDiaryPage() {
 *   return (
 *     <DiaryConfigProvider pageType="main">
 *       <DiaryContextProvider date="2024-01-15">
 *         <DiaryLayout variant="main">
 *           // Your diary content
 *         </DiaryLayout>
 *       </DiaryContextProvider>
 *     </DiaryConfigProvider>
 *   );
 * }
 * ```
 * 
 * 2. Using Compound Components:
 * ```jsx
 * import { DiaryFormCompound, DiarySkinCompound } from './diary/_optimized';
 * 
 * function DiaryContent() {
 *   return (
 *     <div className="grid grid-cols-2 gap-4">
 *       <DiarySkinCompound>
 *         <DiarySkinCompound.Header>
 *           <DiarySkinCompound.StageSelector />
 *           <DiarySkinCompound.Score />
 *         </DiarySkinCompound.Header>
 *         <DiarySkinCompound.Preview />
 *       </DiarySkinCompound>
 *       
 *       <DiaryFormCompound>
 *         <DiaryFormCompound.Subject />
 *         <DiaryFormCompound.Message />
 *         <DiaryFormCompound.Actions>
 *           <DiaryFormCompound.WordCount />
 *           <DiaryFormCompound.SaveButton />
 *         </DiaryFormCompound.Actions>
 *       </DiaryFormCompound>
 *     </div>
 *   );
 * }
 * ```
 * 
 * 3. Using Custom Hooks:
 * ```jsx
 * import { useDiaryState, useDiaryEntry } from './diary/_optimized';
 * 
 * function CustomDiaryComponent() {
 *   const { subject, message, wordCount } = useDiaryState();
 *   const { saveDiaryEntry, validationErrors } = useDiaryEntry();
 *   
 *   // Your component logic
 * }
 * ```
 * 
 * 4. Configuration:
 * ```jsx
 * import { useDiaryConfig, useFeature } from './diary/_optimized';
 * 
 * function ConfigAwareDiaryComponent() {
 *   const config = useDiaryConfig();
 *   const decorationsEnabled = useFeature('enableDecorations');
 *   
 *   // Use configuration in your component
 * }
 * ```
 */
