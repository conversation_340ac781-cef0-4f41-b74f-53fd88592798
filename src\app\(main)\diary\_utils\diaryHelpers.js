/**
 * Shared utility functions for diary functionality
 */

/**
 * Formats a date to YYYY-MM-DD format
 * - The date to format
 * - Formatted date string
 */
export const formatDateToYMD = (inputDate) => {
  const date = new Date(inputDate);
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  return `${year}-${month}-${day}`;
};

/**
 * Counts words in a text string
 * - The text to count words in
 * - Number of words
 */
export const countWords = (text) => {
  if (!text || typeof text !== 'string') return 0;
  return text.trim().split(/\s+/).filter(word => word.length > 0).length;
};

/**
 * Determines if a diary entry is editable based on its status
 * - The diary entry object
 * - Whether the entry is editable
 */
export const isDiaryEntryEditable = (entry) => {
  if (!entry) return true;
  return (
    (entry.status === 'submit' && entry.isResubmission !== false) ||
    (!entry.isResubmission && entry.status !== 'reviewed') ||
    entry.status === 'new'
  );
};

/**
 * Gets the current entry date from entry data or falls back to current date
 *  - The diary entry data
 *  - Fallback date if no entry data
 *  - The current entry date
 */
export const getCurrentEntryDate = (entryData, fallbackDate) => {
  if (entryData?.entryDate) {
    return new Date(entryData.entryDate);
  }
  return fallbackDate ? new Date(fallbackDate) : new Date();
};

/**
 * Determines the next stage based on current stage level
  - Array of available stages
 * - Current stage object
 * - Next stage or null if no next stage
 */
export const getNextStage = (stages, currentStage) => {
  if (!stages || !currentStage) return null;
  
  const currentLevel = currentStage.level;
  return stages.find(stage => stage.level === currentLevel + 1) || null;
};

/**
 * Creates default diary entry parameters for API calls
 *  - Entry date
 *  - Selected skin object
 *  - Selected stage object
 *  - Default entry parameters
 */
export const createDefaultEntryParams = (date, selectedSkin, selectedStage) => ({
  entryDate: formatDateToYMD(new Date(date)),
  title: '',
  content: '',
  skinId: selectedSkin?.id,
  backgroundColor: '#f5f5f5',
  isPrivate: false,
  settingsTemplateId: selectedStage?.id,
});

/**
 * Generates loading state content for diary components
 * - Loading message
 * - Loading component
 */
export const createLoadingState = (message = 'Loading your diary...') => (
  <div className="flex items-center justify-center h-[500px]">
    <div className="text-center">
      <div className="inline-block animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-yellow-400 mb-4"></div>
      <h2 className="text-xl font-semibold">{message}</h2>
    </div>
  </div>
);
