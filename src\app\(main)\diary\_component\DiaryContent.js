import React, { useState } from 'react';
import DiaryPage from './DiaryPage';
import DiaryIconsSidebar from './DiaryIconsSidebar';
import Button from '@/components/Button';
import { useRouter } from 'next/navigation';

const DiaryContent = ({ entries, currentIndex, isMobile = false }) => {
  const router = useRouter();

  // For mobile: show only one entry, for desktop: show two entries (spread)
  const hasNextEntry = !isMobile && currentIndex + 1 < entries.length;

  // Centralized drawer state management
  const [openDrawerId, setOpenDrawerId] = useState(null);

  const handleDrawerToggle = (entryId) => {
    // If the same drawer is clicked, close it; otherwise, open the new one
    setOpenDrawerId(openDrawerId === entryId ? null : entryId);
  };

  return (
    <div className="absolute inset-0 flex max-sm:flex-col sm:overflow-hidden">
      {/* Left page - always shown */}
      {/* <DiaryIconsSidebar position="right-0 top-1/4" /> */}

      <div
        className={`${isMobile ? 'w-full' : 'sm:w-1/2'} bg-[#FDE7E9] ${
          isMobile ? 'rounded-md min-h-[600px] h-full' : 'rounded-tl-md rounded-bl-md'
        } ${
          hasNextEntry ? 'border-r border-gray-300' : ''
        }`}
      >
        <DiaryPage
          entry={entries[currentIndex]}
          isDrawerOpen={openDrawerId === entries[currentIndex]?.id}
          onDrawerToggle={() => handleDrawerToggle(entries[currentIndex]?.id)}
        />
      </div>

      {/* Right page - only shown on desktop if there's a next entry */}
      {hasNextEntry ? (
        <div className="sm:w-1/2 bg-[#FDE7E9] rounded-tr-md rounded-br-md">
          <DiaryPage
            entry={entries[currentIndex + 1]}
            isDrawerOpen={openDrawerId === entries[currentIndex + 1]?.id}
            onDrawerToggle={() =>
              handleDrawerToggle(entries[currentIndex + 1]?.id)
            }
          />
        </div>
      ) : !isMobile ? (
        <div className="h-[98%] my-auto w-full bg-white p-2 rounded-lg flex items-center justify-center">
          <div className="text-center space-y-3">
            <span>End of Diary</span>
            <Button
              buttonText={'Write Diary'}
              onClick={() => router.push('/diary')}
            ></Button>
          </div>
        </div>
      ) : null}
    </div>
  );
};

export default DiaryContent;
