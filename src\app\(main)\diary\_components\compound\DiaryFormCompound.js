'use client';
import React, { createContext, useContext } from 'react';
import { useDiaryFormContext } from '../../_context/DiaryContext';

/**
 * Compound component pattern for diary forms
 * Reduces prop drilling by using context internally
 */

// Form context for compound components
const DiaryFormCompoundContext = createContext(null);

/**
 * Main diary form compound component
 */
const DiaryFormCompound = ({ children, className = '', ...props }) => {
  const formContext = useDiaryFormContext();
  
  return (
    <DiaryFormCompoundContext.Provider value={formContext}>
      <div className={`diary-form-compound ${className}`} {...props}>
        {children}
      </div>
    </DiaryFormCompoundContext.Provider>
  );
};

/**
 * Hook to use form compound context
 */
const useFormCompoundContext = () => {
  const context = useContext(DiaryFormCompoundContext);
  if (!context) {
    throw new Error('Form compound components must be used within DiaryFormCompound');
  }
  return context;
};

/**
 * Subject input compound component
 */
const Subject = ({ className = '', placeholder = 'Enter subject...', ...props }) => {
  const { subject, handleSubjectChange, validationErrors, isEditable } = useFormCompoundContext();
  
  return (
    <div className={`diary-form-subject ${className}`}>
      <input
        type="text"
        value={subject}
        onChange={handleSubjectChange}
        placeholder={placeholder}
        disabled={!isEditable}
        className={`w-full p-2 border rounded-md ${
          validationErrors.subject ? 'border-red-500' : 'border-gray-300'
        } ${!isEditable ? 'bg-gray-100 cursor-not-allowed' : ''}`}
        {...props}
      />
      {validationErrors.subject && (
        <p className="text-red-500 text-sm mt-1">{validationErrors.subject}</p>
      )}
    </div>
  );
};

/**
 * Message textarea compound component
 */
const Message = ({ className = '', placeholder = 'Write your diary entry...', rows = 10, ...props }) => {
  const { message, handleMessageChange, validationErrors, isEditable } = useFormCompoundContext();
  
  return (
    <div className={`diary-form-message ${className}`}>
      <textarea
        value={message}
        onChange={handleMessageChange}
        placeholder={placeholder}
        rows={rows}
        disabled={!isEditable}
        className={`w-full p-2 border rounded-md resize-none ${
          validationErrors.message ? 'border-red-500' : 'border-gray-300'
        } ${!isEditable ? 'bg-gray-100 cursor-not-allowed' : ''}`}
        spellCheck="false"
        autoComplete="off"
        autoCorrect="off"
        autoCapitalize="off"
        data-gramm="false"
        data-gramm_editor="false"
        data-enable-grammarly="false"
        data-grammarly-disable="true"
        data-lt-disable="true"
        data-pwa-disable="true"
        {...props}
      />
      {validationErrors.message && (
        <p className="text-red-500 text-sm mt-1">{validationErrors.message}</p>
      )}
    </div>
  );
};

/**
 * Word count display compound component
 */
const WordCount = ({ className = '', showLabel = true, ...props }) => {
  const { wordCount } = useFormCompoundContext();
  
  return (
    <div className={`diary-form-word-count text-sm text-gray-600 ${className}`} {...props}>
      {showLabel && 'Words: '}{wordCount}
    </div>
  );
};

/**
 * Save button compound component
 */
const SaveButton = ({ 
  className = '', 
  children = 'Save Diary',
  variant = 'primary',
  ...props 
}) => {
  const { saveAndProgress, isSaving, isEditable } = useFormCompoundContext();
  
  const baseClasses = 'px-4 py-2 rounded-md font-medium transition-colors';
  const variantClasses = {
    primary: 'bg-blue-500 hover:bg-blue-600 text-white disabled:bg-gray-400',
    secondary: 'bg-gray-500 hover:bg-gray-600 text-white disabled:bg-gray-400',
    success: 'bg-green-500 hover:bg-green-600 text-white disabled:bg-gray-400'
  };
  
  return (
    <button
      onClick={saveAndProgress}
      disabled={isSaving || !isEditable}
      className={`${baseClasses} ${variantClasses[variant]} ${className}`}
      {...props}
    >
      {isSaving ? 'Saving...' : children}
    </button>
  );
};

/**
 * Edit toggle compound component
 */
const EditToggle = ({ className = '', ...props }) => {
  const { isEdit, setIsEdit, isEditable } = useFormCompoundContext();
  
  if (!isEditable) return null;
  
  return (
    <button
      onClick={() => setIsEdit(!isEdit)}
      className={`px-3 py-1 text-sm rounded-md border ${
        isEdit 
          ? 'bg-red-100 text-red-700 border-red-300 hover:bg-red-200' 
          : 'bg-blue-100 text-blue-700 border-blue-300 hover:bg-blue-200'
      } ${className}`}
      {...props}
    >
      {isEdit ? 'Cancel Edit' : 'Edit'}
    </button>
  );
};

/**
 * Form status indicator compound component
 */
const Status = ({ className = '', ...props }) => {
  const { isSaving, hasValidationErrors, isEditable } = useFormCompoundContext();
  
  let statusText = '';
  let statusClass = '';
  
  if (isSaving) {
    statusText = 'Saving...';
    statusClass = 'text-blue-600';
  } else if (hasValidationErrors) {
    statusText = 'Please fix validation errors';
    statusClass = 'text-red-600';
  } else if (!isEditable) {
    statusText = 'Read only';
    statusClass = 'text-gray-600';
  } else {
    statusText = 'Ready to edit';
    statusClass = 'text-green-600';
  }
  
  return (
    <div className={`diary-form-status text-sm ${statusClass} ${className}`} {...props}>
      {statusText}
    </div>
  );
};

/**
 * Form actions container compound component
 */
const Actions = ({ children, className = '', ...props }) => {
  return (
    <div className={`diary-form-actions flex items-center gap-2 ${className}`} {...props}>
      {children}
    </div>
  );
};

// Attach compound components
DiaryFormCompound.Subject = Subject;
DiaryFormCompound.Message = Message;
DiaryFormCompound.WordCount = WordCount;
DiaryFormCompound.SaveButton = SaveButton;
DiaryFormCompound.EditToggle = EditToggle;
DiaryFormCompound.Status = Status;
DiaryFormCompound.Actions = Actions;

export default DiaryFormCompound;
