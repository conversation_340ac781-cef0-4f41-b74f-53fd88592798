'use client';
import React, { createContext, useContext, useMemo } from 'react';
import { useDiaryState } from '../_hooks/useDiaryState';
import { useDiaryEntry } from '../_hooks/useDiaryEntry';
import { useDiaryStage } from '../_hooks/useDiaryStage';
import { useDiaryDecoration } from '../_hooks/useDiaryDecoration';

/**
 * Diary context to reduce prop drilling
 */
const DiaryContext = createContext(null);

/**
 * Diary context provider component
 * @param {Object} props - Component props
 * @param {string} props.entryId - Optional entry ID for editing
 * @param {string} props.date - Entry date
 * @param {React.ReactNode} props.children - Child components
 */
export const DiaryContextProvider = ({ 
  entryId = null, 
  date, 
  children 
}) => {
  // Use all diary hooks
  const diaryState = useDiaryState();
  const diaryEntry = useDiaryEntry(entryId, date);
  const diaryStage = useDiaryStage(diaryEntry.selectedStageTemplateId);
  const diaryDecoration = useDiaryDecoration();

  // Combine all context values
  const contextValue = useMemo(() => ({
    // State management
    ...diaryState,
    
    // Entry operations
    ...diaryEntry,
    
    // Stage management
    ...diaryStage,
    
    // Decoration management
    ...diaryDecoration,
    
    // Computed values
    currentEntryDate: diaryEntry.diaryEntry?.entryDate || date,
    isEntryEditable: diaryState.isEditable,
    hasValidationErrors: Object.keys(diaryEntry.validationErrors).length > 0,
    
    // Combined operations
    saveAndProgress: async () => {
      const success = await diaryEntry.saveDiaryEntry();
      if (success && diaryStage.nextStage) {
        diaryStage.handleStageChange(diaryStage.nextStage);
      }
      return success;
    },
    
    submitDecorations: async () => {
      if (diaryEntry.diaryEntry?.id) {
        return await diaryDecoration.submitDecorations(diaryEntry.diaryEntry.id);
      }
      return false;
    }
  }), [diaryState, diaryEntry, diaryStage, diaryDecoration, date]);

  return (
    <DiaryContext.Provider value={contextValue}>
      {children}
    </DiaryContext.Provider>
  );
};

/**
 * Hook to use diary context
 * @returns {Object} Diary context
 */
export const useDiaryContext = () => {
  const context = useContext(DiaryContext);
  
  if (!context) {
    throw new Error('useDiaryContext must be used within a DiaryContextProvider');
  }
  
  return context;
};

/**
 * Hook to get diary form context
 * @returns {Object} Form-specific context
 */
export const useDiaryFormContext = () => {
  const context = useDiaryContext();
  
  return {
    subject: context.subject,
    message: context.message,
    wordCount: context.wordCount,
    validationErrors: context.validationErrors,
    isEdit: context.isEdit,
    isSaving: context.isSaving,
    isEditable: context.isEditable,
    handleSubjectChange: context.handleSubjectChange,
    handleMessageChange: context.handleMessageChange,
    setIsEdit: context.setIsEdit,
    saveDiaryEntry: context.saveDiaryEntry,
    saveAndProgress: context.saveAndProgress
  };
};

/**
 * Hook to get diary skin context
 * @returns {Object} Skin-specific context
 */
export const useDiarySkinContext = () => {
  const context = useDiaryContext();
  
  return {
    selectedSkin: context.selectedSkin,
    selectedStage: context.selectedStage,
    selectedStageTemplateId: context.selectedStageTemplateId,
    handleStageChange: context.handleStageChange,
    isDecorating: context.isDecorating,
    decorationItems: context.decorationItems,
    submitDecorations: context.submitDecorations,
    currentEntryDate: context.currentEntryDate,
    diaryEntry: context.diaryEntry
  };
};

/**
 * Hook to get diary decoration context
 * @returns {Object} Decoration-specific context
 */
export const useDiaryDecorationContext = () => {
  const context = useDiaryContext();
  
  return {
    isDecorating: context.isDecorating,
    decorationItems: context.decorationItems,
    selectedDecorationId: context.selectedDecorationId,
    activeTool: context.activeTool,
    brushSettings: context.brushSettings,
    isSubmittingDecoration: context.isSubmittingDecoration,
    decorationSubmissionError: context.decorationSubmissionError,
    decorationSubmissionSuccess: context.decorationSubmissionSuccess,
    canUndo: context.canUndo,
    canRedo: context.canRedo,
    toggleDecorationMode: context.toggleDecorationMode,
    startDecorating: context.startDecorating,
    stopDecorating: context.stopDecorating,
    clearAllDecorations: context.clearAllDecorations,
    submitDecorations: context.submitDecorations,
    loadDecorations: context.loadDecorations,
    resetSubmissionState: context.resetSubmissionState
  };
};

/**
 * Hook to get diary stage context
 * @returns {Object} Stage-specific context
 */
export const useDiaryStageContext = () => {
  const context = useDiaryContext();
  
  return {
    selectedStage: context.selectedStage,
    nextStage: context.nextStage,
    diarySettings: context.diarySettings,
    selectedStageTemplateId: context.selectedStageTemplateId,
    handleStageChange: context.handleStageChange,
    getStageByLevel: context.getStageByLevel,
    canProgressToNextStage: context.canProgressToNextStage,
    fetchDiarySettings: context.fetchDiarySettings
  };
};
