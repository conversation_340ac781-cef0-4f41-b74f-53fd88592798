import { useCallback, useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { toast } from 'sonner';
import useDataFetch from '@/hooks/useDataFetch';
import {
  setTodayEntry,
  setIsLoading,
  setIsSaving,
  selectSelectedSkin,
  selectDiarySubject,
  selectDiaryMessage
} from '@/store/features/diarySlice';
import { 
  formatDateToYMD, 
  createDefaultEntryParams,
  validateDiaryForm 
} from '../_utils/diaryHelpers';
import { diaryValidationSchema } from '../_utils/diaryValidation';
import { DIARY_ENDPOINTS } from '../_utils/diaryConstants';
import api from '@/lib/api';

/**
 * Custom hook for managing diary entry data and operations
 * @param {string} entryId - Optional entry ID for editing existing entries
 * @param {string} date - Entry date
 * @param {Object} selectedStage - Selected stage object
 * @returns {Object} - Diary entry state and operations
 */
export const useDiaryEntry = (entryId = null, date, selectedStage = null) => {
  const dispatch = useDispatch();
  const { user } = useSelector((state) => state.auth);
  const selectedSkin = useSelector(selectSelectedSkin);
  const subject = useSelector(selectDiarySubject);
  const message = useSelector(selectDiaryMessage);
  
  const [validationErrors, setValidationErrors] = useState({});
  const [selectedStageTemplateId, setSelectedStageTemplateId] = useState(null);

  // Fetch diary entry data
  const {
    data: diaryEntryData,
    isLoading: isDiaryEntryLoading,
    refetch: fetchDiaryEntry,
    error: diaryEntryError,
  } = useDataFetch({
    queryKey: ['diary-entry', entryId, date, user?.defaultDiarySkinId],
    endPoint: entryId ? `${DIARY_ENDPOINTS.entries}/${entryId}` : DIARY_ENDPOINTS.entries,
    method: entryId ? 'GET' : 'POST',
    params: entryId
      ? {}
      : createDefaultEntryParams(date, selectedSkin, selectedStage),
    apiConfig: entryId ? {} : { showSuccessToast: false },
  });

  // Update Redux state when data changes
  useEffect(() => {
    if (diaryEntryData) {
      dispatch(setTodayEntry(diaryEntryData));
      dispatch(setIsLoading(false));
    }
  }, [diaryEntryData, dispatch]);

  // Handle loading state
  useEffect(() => {
    dispatch(setIsLoading(isDiaryEntryLoading));
  }, [isDiaryEntryLoading, dispatch]);

  // Validate form data
  const validateForm = useCallback(async () => {
    const formData = { subject, message };
    const { isValid, errors } = await validateDiaryForm(formData);
    setValidationErrors(errors);
    return isValid;
  }, [subject, message]);

  // Save diary entry
  const saveDiaryEntry = useCallback(async () => {
    const isValid = await validateForm();
    if (!isValid) {
      toast.error('Please fix the validation errors');
      return false;
    }

    try {
      dispatch(setIsSaving(true));
      
      const saveData = {
        title: subject,
        content: message,
        settingsTemplateId: selectedStageTemplateId || selectedStage?.id,
      };

      const endpoint = entryId 
        ? `${DIARY_ENDPOINTS.entries}/${entryId}`
        : DIARY_ENDPOINTS.entries;
      
      const method = entryId ? 'PUT' : 'POST';
      
      const response = method === 'PUT'
        ? await api.put(endpoint, saveData)
        : await api.post(endpoint, {
            ...saveData,
            ...createDefaultEntryParams(date, selectedSkin, selectedStage)
          });

      if (response?.data) {
        dispatch(setTodayEntry(response.data));
        toast.success(entryId ? 'Diary updated successfully!' : 'Diary saved successfully!');
        return true;
      }
    } catch (error) {
      console.error('Error saving diary:', error);
      toast.error(error?.response?.data?.message || 'Failed to save diary');
      return false;
    } finally {
      dispatch(setIsSaving(false));
    }
  }, [
    validateForm, dispatch, subject, message, selectedStageTemplateId, 
    selectedStage, entryId, date, selectedSkin
  ]);

  return {
    diaryEntry: diaryEntryData,
    isLoading: isDiaryEntryLoading,
    error: diaryEntryError,
    validationErrors,
    selectedStageTemplateId,
    setSelectedStageTemplateId,
    fetchDiaryEntry,
    saveDiaryEntry,
    validateForm
  };
};
