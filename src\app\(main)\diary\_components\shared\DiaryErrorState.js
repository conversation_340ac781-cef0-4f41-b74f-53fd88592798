'use client';
import React from 'react';
import { Icon } from '@iconify/react';
import Button from '@/components/Button';

/**
 * Reusable error state component for diary pages
 * @param {Object} props - Component props
 * @param {string} props.message - Error message to display
 * @param {string} props.title - Error title
 * @param {Function} props.onRetry - Retry function
 * @param {string} props.retryText - Retry button text
 * @param {string} props.variant - Error variant ('default', 'compact', 'inline')
 * @param {string} props.className - Additional CSS classes
 */
const DiaryErrorState = ({
  message = 'Something went wrong while loading your diary.',
  title = 'Error',
  onRetry,
  retryText = 'Try Again',
  variant = 'default',
  className = ''
}) => {
  const getContainerClasses = () => {
    const baseClasses = ['flex items-center justify-center'];
    
    switch (variant) {
      case 'compact':
        return `${baseClasses.join(' ')} h-32`;
      case 'inline':
        return `${baseClasses.join(' ')} py-4`;
      default:
        return `${baseClasses.join(' ')} h-[500px]`;
    }
  };

  const getIconSize = () => {
    switch (variant) {
      case 'compact':
        return 'h-8 w-8';
      case 'inline':
        return 'h-6 w-6';
      default:
        return 'h-12 w-12';
    }
  };

  const getTitleClasses = () => {
    switch (variant) {
      case 'compact':
        return 'text-lg font-semibold text-red-600 mb-1';
      case 'inline':
        return 'text-sm font-semibold text-red-600 mb-1';
      default:
        return 'text-xl font-semibold text-red-600 mb-2';
    }
  };

  const getMessageClasses = () => {
    switch (variant) {
      case 'compact':
        return 'text-sm text-gray-600 mb-2';
      case 'inline':
        return 'text-xs text-gray-600 mb-2';
      default:
        return 'text-gray-600 mb-4';
    }
  };

  return (
    <div className={`${getContainerClasses()} ${className}`}>
      <div className="text-center max-w-md mx-auto">
        <div className="flex justify-center mb-3">
          <Icon 
            icon="mdi:alert-circle" 
            className={`${getIconSize()} text-red-500`}
          />
        </div>
        
        <h3 className={getTitleClasses()}>
          {title}
        </h3>
        
        <p className={getMessageClasses()}>
          {message}
        </p>
        
        {onRetry && (
          <Button
            buttonText={retryText}
            onClick={onRetry}
            className="bg-red-500 hover:bg-red-600 text-white"
            size={variant === 'compact' || variant === 'inline' ? 'sm' : 'md'}
          />
        )}
      </div>
    </div>
  );
};

export default DiaryErrorState;
