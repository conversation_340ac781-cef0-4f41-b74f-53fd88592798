import { useCallback, useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import {
  setSubject,
  setMessage,
  selectDiarySubject,
  selectDiaryMessage,
  selectSelectedSkin,
  selectIsSaving,
  selectIsLoading,
  selectTodayEntry,
  selectIsSkinModalOpen,
  selectLayoutBackground
} from '@/store/features/diarySlice';
import { countWords, isDiaryEntryEditable } from '../_utils/diaryHelpers';

/**
 * Custom hook for managing diary form state and operations
 * - Diary state and operations
 */
export const useDiaryState = () => {
  const dispatch = useDispatch();
  
  // Redux selectors
  const subject = useSelector(selectDiarySubject);
  const message = useSelector(selectDiaryMessage);
  const selectedSkin = useSelector(selectSelectedSkin);
  const isSaving = useSelector(selectIsSaving);
  const isLoading = useSelector(selectIsLoading);
  const todayEntry = useSelector(selectTodayEntry);
  const isSkinModalOpen = useSelector(selectIsSkinModalOpen);
  const layoutBackground = useSelector(selectLayoutBackground);

  // Local state
  const [wordCount, setWordCount] = useState(0);
  const [isEdit, setIsEdit] = useState(false);

  // Update word count when message changes
  useEffect(() => {
    setWordCount(countWords(message));
  }, [message]);

  // Determine if entry is editable
  const isEditable = isDiaryEntryEditable(todayEntry);

  // Update subject
  const updateSubject = useCallback((newSubject) => {
    dispatch(setSubject(newSubject));
  }, [dispatch]);

  // Update message
  const updateMessage = useCallback((newMessage) => {
    dispatch(setMessage(newMessage));
  }, [dispatch]);

  // Handle subject change with validation
  const handleSubjectChange = useCallback((e) => {
    const newSubject = e.target.value;
    updateSubject(newSubject);
  }, [updateSubject]);

  // Handle message change with validation
  const handleMessageChange = useCallback((e) => {
    const newMessage = e.target.value;
    updateMessage(newMessage);
  }, [updateMessage]);

  // Toggle edit mode
  const toggleEditMode = useCallback(() => {
    setIsEdit(!isEdit);
  }, [isEdit]);

  // Enable edit mode
  const enableEditMode = useCallback(() => {
    setIsEdit(true);
  }, []);

  // Disable edit mode
  const disableEditMode = useCallback(() => {
    setIsEdit(false);
  }, []);

  // Check if form has unsaved changes
  const hasUnsavedChanges = useCallback(() => {
    if (!todayEntry) return subject.length > 0 || message.length > 0;
    
    return (
      subject !== (todayEntry.title || '') ||
      message !== (todayEntry.content || '')
    );
  }, [subject, message, todayEntry]);

  // Get form data
  const getFormData = useCallback(() => ({
    subject,
    message,
    wordCount
  }), [subject, message, wordCount]);

  // Reset form to entry data
  const resetFormToEntry = useCallback(() => {
    if (todayEntry) {
      dispatch(setSubject(todayEntry.title || ''));
      dispatch(setMessage(todayEntry.content || ''));
    } else {
      dispatch(setSubject(''));
      dispatch(setMessage(''));
    }
  }, [dispatch, todayEntry]);

  return {
    // Form state
    subject,
    message,
    wordCount,
    isEdit,
    isEditable,
    
    // Redux state
    selectedSkin,
    isSaving,
    isLoading,
    todayEntry,
    isSkinModalOpen,
    layoutBackground,
    
    // Form operations
    updateSubject,
    updateMessage,
    handleSubjectChange,
    handleMessageChange,
    
    // Edit mode operations
    toggleEditMode,
    enableEditMode,
    disableEditMode,
    setIsEdit,
    
    // Utility operations
    hasUnsavedChanges,
    getFormData,
    resetFormToEntry
  };
};
