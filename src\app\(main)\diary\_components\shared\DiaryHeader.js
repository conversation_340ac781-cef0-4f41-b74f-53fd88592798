'use client';
import React from 'react';
import { ButtonIcon } from '@/components/Button';
import DiaryIconsSidebar from '../../_component/DiaryIconsSidebar';

/**
 * Reusable diary header component with consistent layout
 * @param {Object} props - Component props
 * @param {boolean} props.showSideIcons - Whether to show side icons
 * @param {Function} props.onCloseSideIcons - Function to close side icons
 * @param {boolean} props.isDecorating - Whether decoration mode is active
 * @param {Function} props.onClose - Function to close diary
 * @param {string} props.variant - Header variant ('main', 'item')
 * @param {React.ReactNode} props.children - Additional header content
 * @param {string} props.className - Additional CSS classes
 */
const DiaryHeader = ({
  showSideIcons = false,
  onCloseSideIcons,
  isDecorating = false,
  onClose,
  variant = 'main',
  children,
  className = ''
}) => {
  const getHeaderClasses = () => {
    const baseClasses = ['absolute'];
    
    switch (variant) {
      case 'item':
        return `${baseClasses.join(' ')} right-0 -top-14 sm:top-3`;
      default:
        return `${baseClasses.join(' ')} -right-3 -top-14 2xl:-right-14 2xl:-top-3`;
    }
  };

  const getSideIconsClasses = () => {
    const baseClasses = ['absolute left-1/2 -translate-x-1/2 bg-gradient-to-b from-yellow-300 via-yellow-400 to-yellow-500 px-4 pr-8 py-1 rounded-full'];
    
    return `${baseClasses.join(' ')} max-sm:-top-8 top-0`;
  };

  return (
    <>
      {/* Side Icons */}
      {showSideIcons && (
        <div className={getSideIconsClasses()}>
          <DiaryIconsSidebar
            isOpen={showSideIcons}
            onClose={onCloseSideIcons}
            isEmojiSelectorOpen={isDecorating}
          />
        </div>
      )}

      {/* Close Button */}
      {onClose && (
        <div className={`${getHeaderClasses()} ${className}`}>
          <ButtonIcon
            icon="mdi:close"
            innerBtnCls="h-10 w-10 cursor-pointer"
            btnIconCls="h-5 w-5"
            aria-label="close diary"
            onClick={onClose}
          />
        </div>
      )}

      {/* Additional header content */}
      {children}
    </>
  );
};

export default DiaryHeader;
