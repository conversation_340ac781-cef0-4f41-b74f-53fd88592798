import { useState, useEffect, useCallback } from 'react';
import { useDispatch } from 'react-redux';
import useDataFetch from '@/hooks/useDataFetch';
import { setSelectedStage } from '@/store/features/diarySlice';
import { getNextStage } from '../_utils/diaryHelpers';

/**
 * Custom hook for managing diary stage selection and progression
 * - Currently selected stage template ID
 * - Stage management state and operations
 */
export const useDiaryStage = (selectedStageTemplateId) => {
  const dispatch = useDispatch();
  const [selectedStage, setSelectedStageState] = useState(null);
  const [nextStage, setNextStage] = useState(null);

  // Fetch diary settings (stages)
  const { 
    data: diarySettingsData, 
    refetch: fetchDiarySettings 
  } = useDataFetch({
    queryKey: ['diary-settings'],
    endPoint: '/diary/settings',
    method: 'GET',
    params: {},
    enabled: false, // Only fetch when manually triggered
  });

  // Update selected stage when template ID or settings data changes
  useEffect(() => {
    if (diarySettingsData?.items && selectedStageTemplateId) {
      const stage = diarySettingsData.items.find(
        (item) => item.id === selectedStageTemplateId
      );
      if (stage) {
        setSelectedStageState(stage);
        dispatch(setSelectedStage(stage));
        
        // Calculate next stage
        const next = getNextStage(diarySettingsData.items, stage);
        setNextStage(next);
      }
    }
  }, [diarySettingsData, selectedStageTemplateId, dispatch]);

  // Handle stage change
  const handleStageChange = useCallback((newStage) => {
    setSelectedStageState(newStage);
    dispatch(setSelectedStage(newStage));
    
    // Calculate next stage
    if (diarySettingsData?.items) {
      const next = getNextStage(diarySettingsData.items, newStage);
      setNextStage(next);
    }
  }, [dispatch, diarySettingsData]);

  // Get stage by level
  const getStageByLevel = useCallback((level) => {
    if (!diarySettingsData?.items) return null;
    return diarySettingsData.items.find(stage => stage.level === level);
  }, [diarySettingsData]);

  // Check if stage progression is available
  const canProgressToNextStage = useCallback(() => {
    return nextStage !== null;
  }, [nextStage]);

  return {
    selectedStage,
    nextStage,
    diarySettings: diarySettingsData,
    fetchDiarySettings,
    handleStageChange,
    getStageByLevel,
    canProgressToNextStage
  };
};
