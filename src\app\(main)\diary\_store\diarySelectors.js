import { createSelector } from '@reduxjs/toolkit';

// Base selectors
const selectDiaryState = (state) => state.diary;
const selectCanvasStates = (state) => state.canvas;

// Memoized diary selectors
export const selectDiaryFormData = createSelector(
  [selectDiaryState],
  (diary) => ({
    subject: diary.subject,
    message: diary.message,
    date: diary.date
  })
);

export const selectDiaryUIState = createSelector(
  [selectDiaryState],
  (diary) => ({
    isLoading: diary.isLoading,
    isSaving: diary.isSaving,
    isSkinModalOpen: diary.isSkinModalOpen,
    isTodayDiaryOpen: diary.isTodayDiaryOpen,
    isDecorating: diary.isDecorating
  })
);

export const selectDiaryEntryState = createSelector(
  [selectDiaryState],
  (diary) => ({
    todayEntry: diary.todayEntry,
    selectedSkin: diary.selectedSkin,
    selectedStage: diary.selectedStage,
    layoutBackground: diary.layoutBackground
  })
);

export const selectDecorationState = createSelector(
  [selectDiaryState],
  (diary) => ({
    isDecorating: diary.isDecorating,
    decorationItems: diary.decorationItems,
    selectedDecorationId: diary.selectedDecorationId,
    activeTool: diary.activeTool,
    brushSettings: diary.brushSettings,
    isSubmittingDecoration: diary.isSubmittingDecoration,
    decorationSubmissionError: diary.decorationSubmissionError,
    decorationSubmissionSuccess: diary.decorationSubmissionSuccess,
    canUndo: diary.decorationHistory.past.length > 0,
    canRedo: diary.decorationHistory.future.length > 0
  })
);

export const selectMyDiaryState = createSelector(
  [selectDiaryState],
  (diary) => ({
    isOpen: diary.myDiary?.isOpen || false,
    currentIndex: diary.myDiary?.currentIndex || 0,
    currentPage: diary.myDiary?.currentPage || 1,
    coverPhotoUrl: diary.myDiary?.coverPhotoUrl || null
  })
);

// Canvas selectors
export const selectCanvasState = createSelector(
  [selectCanvasStates],
  (canvas) => ({
    items: canvas.items,
    background: canvas.background,
    selectedId: canvas.selectedId,
    previewMode: canvas.previewMode
  })
);

// Combined selectors for complex operations
export const selectDiaryEditableState = createSelector(
  [selectDiaryEntryState, selectDiaryFormData],
  (entryState, formData) => {
    const { todayEntry } = entryState;
    const isEditable = !todayEntry || 
      (todayEntry.status === 'submit' && todayEntry.isResubmission !== false) ||
      (!todayEntry.isResubmission && todayEntry.status !== 'reviewed') ||
      todayEntry.status === 'new';
    
    const hasUnsavedChanges = todayEntry ? 
      (formData.subject !== (todayEntry.title || '') ||
       formData.message !== (todayEntry.content || '')) :
      (formData.subject.length > 0 || formData.message.length > 0);

    return {
      isEditable,
      hasUnsavedChanges,
      canSave: isEditable && (formData.subject.trim() || formData.message.trim())
    };
  }
);

export const selectDiaryLoadingState = createSelector(
  [selectDiaryUIState],
  (uiState) => ({
    isLoading: uiState.isLoading,
    isSaving: uiState.isSaving,
    isAnyLoading: uiState.isLoading || uiState.isSaving
  })
);

// Selectors for specific components
export const selectDiaryFormProps = createSelector(
  [selectDiaryFormData, selectDiaryEntryState, selectDiaryEditableState, selectDiaryLoadingState],
  (formData, entryState, editableState, loadingState) => ({
    ...formData,
    ...entryState,
    ...editableState,
    ...loadingState
  })
);

export const selectDiarySkinProps = createSelector(
  [selectDiaryEntryState, selectDecorationState],
  (entryState, decorationState) => ({
    ...entryState,
    isDecorating: decorationState.isDecorating,
    decorationItems: decorationState.decorationItems
  })
);

export const selectDiaryContainerProps = createSelector(
  [selectDiaryUIState, selectDecorationState],
  (uiState, decorationState) => ({
    isOpen: uiState.isTodayDiaryOpen,
    isDecorating: decorationState.isDecorating,
    isLoading: uiState.isLoading
  })
);
