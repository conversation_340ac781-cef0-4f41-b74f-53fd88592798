'use client';
import React, { createContext, useContext, useMemo } from 'react';
import { 
  getDiaryPageConfig, 
  isFeatureEnabled, 
  getApiEndpoint,
  getValidationRules,
  getDecorationLimit,
  STAGE_CONFIG,
  DECORATION_CONFIG,
  UI_CONFIG,
  PERFORMANCE_CONFIG
} from './diaryConfig';

/**
 * Diary configuration context
 */
const DiaryConfigContext = createContext(null);

/**
 * Diary configuration provider component
 * @param {Object} props - Component props
 * @param {string} props.pageType - Type of diary page
 * @param {Object} props.overrides - Configuration overrides
 * @param {React.ReactNode} props.children - Child components
 */
export const DiaryConfigProvider = ({ 
  pageType = 'main', 
  overrides = {}, 
  children 
}) => {
  const config = useMemo(() => {
    const baseConfig = getDiaryPageConfig(pageType);
    
    return {
      // Page configuration
      page: { ...baseConfig, ...overrides.page },
      
      // Feature flags
      features: {
        decorations: isFeatureEnabled('enableDecorations') && baseConfig.enableDecorations,
        stageProgression: isFeatureEnabled('enableStageProgression') && baseConfig.enableStageProgression,
        calendar: isFeatureEnabled('enableCalendarFilter'),
        myDiary: isFeatureEnabled('enableMyDiary'),
        sharedDiaries: isFeatureEnabled('enableSharedDiaries'),
        autoSave: isFeatureEnabled('enableAutoSave'),
        ...overrides.features
      },
      
      // Stage configuration
      stage: { ...STAGE_CONFIG, ...overrides.stage },
      
      // Decoration configuration
      decoration: { ...DECORATION_CONFIG, ...overrides.decoration },
      
      // UI configuration
      ui: { ...UI_CONFIG, ...overrides.ui },
      
      // Performance configuration
      performance: { ...PERFORMANCE_CONFIG, ...overrides.performance },
      
      // Helper functions
      getApiEndpoint,
      getValidationRules,
      getDecorationLimit,
      isFeatureEnabled: (featureName) => {
        // Check both global feature flags and local config
        return isFeatureEnabled(featureName) && 
               (config.features[featureName] !== false);
      }
    };
  }, [pageType, overrides]);

  return (
    <DiaryConfigContext.Provider value={config}>
      {children}
    </DiaryConfigContext.Provider>
  );
};

/**
 * Hook to use diary configuration
 * @returns {Object} Diary configuration
 */
export const useDiaryConfig = () => {
  const context = useContext(DiaryConfigContext);
  
  if (!context) {
    throw new Error('useDiaryConfig must be used within a DiaryConfigProvider');
  }
  
  return context;
};

/**
 * Hook to get page-specific configuration
 * @returns {Object} Page configuration
 */
export const usePageConfig = () => {
  const { page } = useDiaryConfig();
  return page;
};

/**
 * Hook to check feature availability
 * @param {string} featureName - Name of the feature to check
 * @returns {boolean} Whether the feature is enabled
 */
export const useFeature = (featureName) => {
  const { features, isFeatureEnabled } = useDiaryConfig();
  
  // Check if feature exists in local features config
  if (features.hasOwnProperty(featureName)) {
    return features[featureName];
  }
  
  // Fall back to global feature flag
  return isFeatureEnabled(featureName);
};

/**
 * Hook to get API configuration
 * @returns {Object} API configuration helpers
 */
export const useApiConfig = () => {
  const { getApiEndpoint } = useDiaryConfig();
  
  return {
    getEndpoint: getApiEndpoint,
    buildEndpoint: (key, params) => getApiEndpoint(key, params)
  };
};

/**
 * Hook to get validation configuration
 * @returns {Object} Validation configuration helpers
 */
export const useValidationConfig = () => {
  const { getValidationRules } = useDiaryConfig();
  
  return {
    getRules: getValidationRules,
    getFieldRules: (fieldName) => getValidationRules(fieldName)
  };
};

/**
 * Hook to get decoration configuration
 * @returns {Object} Decoration configuration and helpers
 */
export const useDecorationConfig = () => {
  const { decoration, getDecorationLimit } = useDiaryConfig();
  
  return {
    config: decoration,
    getLimit: getDecorationLimit,
    isTypeAllowed: (type) => decoration.allowedDecorationTypes.includes(type),
    getMaxDecorations: () => decoration.maxDecorationsPerEntry
  };
};
