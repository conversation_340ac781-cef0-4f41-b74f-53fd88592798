import { useCallback } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { toast } from 'sonner';
import api from '@/lib/api';
import {
  selectIsDecorating,
  selectDecorationItems,
  selectIsSubmittingDecoration,
  selectDecorationSubmissionError,
  selectDecorationSubmissionSuccess,
  setIsDecorating,
  setDecorationItems,
  setIsSubmittingDecoration,
  setDecorationSubmissionError,
  setDecorationSubmissionSuccess,
  resetDecorationSubmissionState,
  clearDecorations,
  setSelectedDecorationId
} from '@/store/features/diarySlice';

/**
 * Custom hook for managing diary decoration functionality
 * - Decoration state and operations
 */
export const useDiaryDecoration = () => {
  const dispatch = useDispatch();
  
  // Selectors
  const isDecorating = useSelector(selectIsDecorating);
  const decorationItems = useSelector(selectDecorationItems);
  const isSubmittingDecoration = useSelector(selectIsSubmittingDecoration);
  const decorationSubmissionError = useSelector(selectDecorationSubmissionError);
  const decorationSubmissionSuccess = useSelector(selectDecorationSubmissionSuccess);

  // Toggle decoration mode
  const toggleDecorationMode = useCallback(() => {
    dispatch(setIsDecorating(!isDecorating));
  }, [dispatch, isDecorating]);

  // Start decoration mode
  const startDecorating = useCallback(() => {
    dispatch(setIsDecorating(true));
  }, [dispatch]);

  // Stop decoration mode
  const stopDecorating = useCallback(() => {
    dispatch(setIsDecorating(false));
    dispatch(setSelectedDecorationId(null));
  }, [dispatch]);

  // Clear all decorations
  const clearAllDecorations = useCallback(() => {
    dispatch(clearDecorations());
  }, [dispatch]);

  // Submit decorations to server
  const submitDecorations = useCallback(async (entryId) => {
    if (!entryId || !decorationItems || decorationItems.length === 0) {
      toast.error('No decorations to save');
      return false;
    }

    try {
      dispatch(setIsSubmittingDecoration(true));
      dispatch(resetDecorationSubmissionState());

      const response = await api.post(`/diary/entries/${entryId}/decorations`, {
        decorations: decorationItems
      });

      if (response?.data) {
        dispatch(setDecorationSubmissionSuccess(true));
        toast.success('Decorations saved successfully!');
        return true;
      }
    } catch (error) {
      console.error('Error submitting decorations:', error);
      const errorMessage = error?.response?.data?.message || 'Failed to save decorations';
      dispatch(setDecorationSubmissionError(errorMessage));
      toast.error(errorMessage);
      return false;
    } finally {
      dispatch(setIsSubmittingDecoration(false));
    }
  }, [dispatch, decorationItems]);

  // Load decorations from server
  const loadDecorations = useCallback(async (entryId) => {
    if (!entryId) return;

    try {
      const response = await api.get(`/diary/entries/${entryId}/decorations`);
      if (response?.data?.decorations) {
        dispatch(setDecorationItems(response.data.decorations));
      }
    } catch (error) {
      console.error('Error loading decorations:', error);
      // Don't show error toast for loading decorations as it's not critical
    }
  }, [dispatch]);

  // Reset decoration submission state
  const resetSubmissionState = useCallback(() => {
    dispatch(resetDecorationSubmissionState());
  }, [dispatch]);

  return {
    // State
    isDecorating,
    decorationItems,
    isSubmittingDecoration,
    decorationSubmissionError,
    decorationSubmissionSuccess,
    
    // Actions
    toggleDecorationMode,
    startDecorating,
    stopDecorating,
    clearAllDecorations,
    submitDecorations,
    loadDecorations,
    resetSubmissionState
  };
};
