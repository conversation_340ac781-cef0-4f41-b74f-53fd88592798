'use client';
import React, { useState } from 'react';
import Button from '@/components/Button';
import CalendarFilter from '../../_component/modalContents/share/CalendarFilter';

/**
 * Reusable calendar button component for diary pages
 * - Component props
 * - Function called when date is selected
 * - Function to fetch diary entry
 * - Button variant ('default', 'cover')
 * - Additional CSS classes
 */
const DiaryCalendarButton = ({
  onDateSelect,
  fetchDiaryEntry,
  variant = 'default',
  className = ''
}) => {
  const [showCalendar, setShowCalendar] = useState(false);

  const handleDateSelect = () => {
    if (onDateSelect) {
      onDateSelect();
    }
    if (fetchDiaryEntry) {
      fetchDiaryEntry();
    }
  };

  const getButtonContainerClasses = () => {
    switch (variant) {
      case 'cover':
        return '';
      default:
        return 'absolute right-0 -top-14 sm:top-3';
    }
  };

  const getCalendarContainerClasses = () => {
    const baseClasses = [
      'block absolute max-w-xl min-w-80 max-h-[400px] max-sm:max-h-[300px]',
      'overflow-y-auto bg-white border rounded-lg shadow-lg z-20 mt-8 top-0 right-0'
    ];

    return showCalendar ? baseClasses.join(' ') : 'hidden';
  };

  return (
    <div className={`${getButtonContainerClasses()} ${className}`}>
      <Button
        icon="material-symbols:calendar-month-outline"
        buttonText="Find Diary"
        onClick={() => setShowCalendar(!showCalendar)}
      />

      {showCalendar && (
        <div className={getCalendarContainerClasses()}>
          <CalendarFilter
            onDateSelect={handleDateSelect}
            onClose={() => setShowCalendar(false)}
          />
        </div>
      )}
    </div>
  );
};

export default DiaryCalendarButton;
