/**
 * Centralized configuration for diary functionality
 */

// Diary page configurations
export const DIARY_PAGE_CONFIGS = {
  main: {
    variant: 'main',
    showSideIcons: true,
    showCalendar: false,
    enableDecorations: true,
    enableStageProgression: true,
    defaultSkin: null,
    containerClasses: 'min-h-[668px]',
    headerPosition: '-right-3 -top-14 2xl:-right-14 2xl:-top-3'
  },
  
  myDiary: {
    variant: 'my-diary',
    showSideIcons: true,
    showCalendar: true,
    enableDecorations: false,
    enableStageProgression: false,
    defaultSkin: null,
    containerClasses: 'min-h-[600px] max-h-[calc(100vh-180px)]',
    headerPosition: 'lg:right-[-60px] right-0 -top-14 lg:top-4'
  },
  
  item: {
    variant: 'item',
    showSideIcons: true,
    showCalendar: true,
    enableDecorations: true,
    enableStageProgression: true,
    defaultSkin: null,
    containerClasses: 'min-h-[668px]',
    headerPosition: 'right-0 -top-14 sm:top-3'
  }
};

// Stage progression configuration
export const STAGE_CONFIG = {
  enableAutoProgression: true,
  progressionTriggers: {
    wordCount: true,
    timeSpent: false,
    completion: true
  },
  progressionThresholds: {
    minWordCount: 50,
    minTimeSpent: 300, // 5 minutes in seconds
    completionPercentage: 100
  },
  maxStageLevel: 10,
  defaultStageLevel: 1
};

// Decoration configuration
export const DECORATION_CONFIG = {
  enableDecorations: true,
  maxDecorationsPerEntry: 20,
  allowedDecorationTypes: [
    'emoji',
    'sticker',
    'drawing',
    'text',
    'image'
  ],
  decorationLimits: {
    emoji: 10,
    sticker: 5,
    drawing: 3,
    text: 5,
    image: 2
  },
  defaultBrushSettings: {
    size: 5,
    color: '#000000',
    opacity: 1
  },
  canvasSettings: {
    width: 800,
    height: 600,
    backgroundColor: 'transparent'
  }
};

// Validation configuration
export const VALIDATION_CONFIG = {
  subject: {
    required: true,
    minLength: 1,
    maxLength: 200,
    allowEmpty: false
  },
  message: {
    required: true,
    minLength: 1,
    maxLength: 5000,
    allowEmpty: false
  },
  wordCount: {
    min: 0,
    max: 1000,
    countMethod: 'split' // 'split' or 'regex'
  }
};

// API configuration
export const API_CONFIG = {
  endpoints: {
    entries: '/diary/entries',
    settings: '/diary/settings',
    search: '/diary/entries/search',
    sharedEntries: 'diary/shared-entries',
    decorations: '/diary/entries/{id}/decorations'
  },
  defaultParams: {
    backgroundColor: '#f5f5f5',
    isPrivate: false,
    title: '',
    content: ''
  },
  requestConfig: {
    timeout: 30000,
    retries: 3,
    retryDelay: 1000
  }
};

// UI configuration
export const UI_CONFIG = {
  animations: {
    enableTransitions: true,
    transitionDuration: 300,
    enableLoadingSpinners: true,
    enableProgressBars: true
  },
  responsive: {
    breakpoints: {
      mobile: 768,
      tablet: 1024,
      desktop: 1280
    },
    mobileOptimizations: {
      reducedAnimations: true,
      simplifiedUI: true,
      touchOptimized: true
    }
  },
  themes: {
    default: {
      primaryColor: '#FDE7E9',
      secondaryColor: '#FFD3D8',
      accentColor: '#ECB306',
      backgroundColor: '#f5f5f5'
    }
  }
};

// Feature flags
export const FEATURE_FLAGS = {
  enableMyDiary: true,
  enableDecorations: true,
  enableStageProgression: true,
  enableSharedDiaries: true,
  enableCalendarFilter: true,
  enableAutoSave: false,
  enableOfflineMode: false,
  enableRealTimeSync: false
};

// Performance configuration
export const PERFORMANCE_CONFIG = {
  pagination: {
    defaultPageSize: 10,
    maxPageSize: 50,
    enableInfiniteScroll: false
  },
  caching: {
    enableQueryCache: true,
    cacheTimeout: 300000, // 5 minutes
    maxCacheSize: 100
  },
  optimization: {
    enableMemoization: true,
    enableVirtualization: false,
    enableLazyLoading: true
  }
};

/**
 * Get configuration for a specific diary page
 * - Type of diary page ('main', 'myDiary', 'item')
 * - Page configuration
 */
export const getDiaryPageConfig = (pageType) => {
  return DIARY_PAGE_CONFIGS[pageType] || DIARY_PAGE_CONFIGS.main;
};

/**
 * Check if a feature is enabled
 * - Name of the feature
 * - Whether the feature is enabled
 */
export const isFeatureEnabled = (featureName) => {
  return FEATURE_FLAGS[featureName] || false;
};

/**
 * Get API endpoint with parameters replaced
 * - Key of the endpoint
 * - Parameters to replace in the endpoint
 * - Complete endpoint URL
 */
export const getApiEndpoint = (endpointKey, params = {}) => {
  let endpoint = API_CONFIG.endpoints[endpointKey];
  
  if (!endpoint) {
    throw new Error(`Unknown API endpoint: ${endpointKey}`);
  }
  
  // Replace parameters in the endpoint
  Object.keys(params).forEach(key => {
    endpoint = endpoint.replace(`{${key}}`, params[key]);
  });
  
  return endpoint;
};

/**
 * Get validation rules for a field
 * - Name of the field
 * - Validation rules
 */
export const getValidationRules = (fieldName) => {
  return VALIDATION_CONFIG[fieldName] || {};
};

/**
 * Get decoration limits for a type
 * - Type of decoration
 * - Maximum allowed decorations of this type
 */
export const getDecorationLimit = (decorationType) => {
  return DECORATION_CONFIG.decorationLimits[decorationType] || 0;
};
