'use client';
import React from 'react';
import <PERSON><PERSON>ontainer from './DiaryContainer';
import DiaryLoadingState from './DiaryLoadingState';
import Diary<PERSON>rrorState from './DiaryErrorState';
import DiaryHeader from './DiaryHeader';
import { DIARY_LAYOUT_CLASSES } from '../../_utils/diaryConstants';

/**
 * Main diary layout component that combines all shared layout elements
 * @param {Object} props - Component props
 * @param {boolean} props.isOpen - Whether diary is open
 * @param {boolean} props.isLoading - Whether content is loading
 * @param {boolean} props.isDecorating - Whether decoration mode is active
 * @param {Object} props.error - Error object if any
 * @param {Function} props.onRetry - Retry function for errors
 * @param {Function} props.onClose - Function to close diary
 * @param {boolean} props.showSideIcons - Whether to show side icons
 * @param {Function} props.onCloseSideIcons - Function to close side icons
 * @param {string} props.variant - Layout variant ('main', 'my-diary', 'item')
 * @param {string} props.loadingMessage - Custom loading message
 * @param {React.ReactNode} props.children - Main content
 * @param {React.ReactNode} props.headerContent - Additional header content
 * @param {string} props.className - Additional CSS classes
 */
const DiaryLayout = ({
  isOpen = false,
  isLoading = false,
  isDecorating = false,
  error = null,
  onRetry,
  onClose,
  showSideIcons = false,
  onCloseSideIcons,
  variant = 'main',
  loadingMessage,
  children,
  headerContent,
  className = ''
}) => {
  // Render error state
  if (error) {
    return (
      <div className={`${DIARY_LAYOUT_CLASSES.mainContainer} ${className}`}>
        <DiaryContainer variant={variant} isOpen={isOpen}>
          <DiaryErrorState
            message={error.message || 'Failed to load diary'}
            onRetry={onRetry}
          />
        </DiaryContainer>
      </div>
    );
  }

  // Render loading state
  if (isLoading) {
    return (
      <div className={`${DIARY_LAYOUT_CLASSES.mainContainer} ${className}`}>
        <DiaryContainer variant={variant} isOpen={isOpen} isLoading={isLoading}>
          <DiaryLoadingState message={loadingMessage} />
        </DiaryContainer>
      </div>
    );
  }

  // Render main content
  return (
    <div className={`${DIARY_LAYOUT_CLASSES.mainContainer} ${className}`}>
      <DiaryContainer 
        variant={variant} 
        isOpen={isOpen} 
        isDecorating={isDecorating}
      >
        <DiaryHeader
          showSideIcons={showSideIcons}
          onCloseSideIcons={onCloseSideIcons}
          isDecorating={isDecorating}
          onClose={onClose}
          variant={variant}
        >
          {headerContent}
        </DiaryHeader>

        {children}
      </DiaryContainer>
    </div>
  );
};

export default DiaryLayout;
