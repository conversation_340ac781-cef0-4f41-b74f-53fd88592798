import * as yup from 'yup';

/**
 * Shared validation schema for diary forms
 * Used across all diary routes to ensure consistent validation
 */
export const diaryValidationSchema = yup.object().shape({
  subject: yup
    .string()
    .trim()
    .required('Subject is required')
    .min(1, 'Subject cannot be empty'),
  message: yup
    .string()
    .trim()
    .required('Content is required')
    .min(1, 'Content cannot be empty'),
});

/**
 * Validates diary form data against the schema
 * @param {Object} data - The form data to validate
 * @returns {Object} - { isValid: boolean, errors: Object }
 */
export const validateDiaryForm = async (data) => {
  try {
    await diaryValidationSchema.validate(data, { abortEarly: false });
    return { isValid: true, errors: {} };
  } catch (error) {
    const validationErrors = {};
    error.inner.forEach((err) => {
      validationErrors[err.path] = err.message;
    });
    return { isValid: false, errors: validationErrors };
  }
};
