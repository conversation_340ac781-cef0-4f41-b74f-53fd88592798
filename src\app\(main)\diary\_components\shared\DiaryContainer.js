'use client';
import React from 'react';
import { DIARY_CONTAINER_CLASSES, DIARY_LAYOUT_CLASSES } from '../../_utils/diaryConstants';

/**
 * Reusable diary container component with consistent styling
 * @param {Object} props - Component props
 * @param {boolean} props.isOpen - Whether diary is open
 * @param {boolean} props.isDecorating - Whether decoration mode is active
 * @param {boolean} props.isLoading - Whether content is loading
 * @param {string} props.variant - Container variant ('main', 'my-diary', 'item')
 * @param {React.ReactNode} props.children - Child components
 * @param {string} props.className - Additional CSS classes
 * @param {Object} props.style - Additional inline styles
 */
const DiaryContainer = ({
  isOpen = false,
  isDecorating = false,
  isLoading = false,
  variant = 'main',
  children,
  className = '',
  style = {},
  ...props
}) => {
  // Get width classes based on variant and open state
  const getWidthClasses = () => {
    switch (variant) {
      case 'my-diary':
        return isOpen 
          ? DIARY_LAYOUT_CLASSES.myDiaryOpenWidth 
          : DIARY_LAYOUT_CLASSES.myDiaryClosedWidth;
      case 'item':
        return DIARY_LAYOUT_CLASSES.openWidth;
      default:
        return isOpen 
          ? DIARY_LAYOUT_CLASSES.openWidth 
          : DIARY_LAYOUT_CLASSES.closedWidth;
    }
  };

  // Get container classes
  const getContainerClasses = () => {
    const baseClasses = [
      DIARY_LAYOUT_CLASSES.diaryWrapper,
      getWidthClasses()
    ];

    if (variant === 'my-diary') {
      const bgClass = !isOpen ? 'bg-[#FDE7E9]' : '';
      baseClasses.push('min-h-[600px] max-h-[calc(100vh-180px)]', bgClass);
    } else {
      baseClasses.push('min-h-[668px]');
    }

    return baseClasses.filter(Boolean).join(' ');
  };

  // Get inner container classes for diary content
  const getInnerContainerClasses = () => {
    const baseClasses = [DIARY_CONTAINER_CLASSES.base];
    
    if (isDecorating) {
      baseClasses.push(DIARY_CONTAINER_CLASSES.withDecorating);
    } else {
      baseClasses.push(DIARY_CONTAINER_CLASSES.withoutDecorating);
    }

    if (!isOpen) {
      baseClasses.push(DIARY_CONTAINER_CLASSES.minHeight);
    }

    return baseClasses.join(' ');
  };

  return (
    <div 
      className={`${getContainerClasses()} ${className}`}
      style={style}
      {...props}
    >
      {variant === 'my-diary' ? (
        // My diary has different structure
        children
      ) : (
        // Main diary and item diary use inner container
        <div className={getInnerContainerClasses()}>
          {children}
        </div>
      )}
    </div>
  );
};

export default DiaryContainer;
