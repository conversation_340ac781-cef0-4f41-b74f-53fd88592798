/**
 * Shared constants for diary functionality
 */

// Diary entry statuses
export const DIARY_STATUS = {
  NEW: 'new',
  SUBMIT: 'submit',
  REVIEWED: 'reviewed',
  DRAFT: 'draft'
};

// Default diary settings
export const DEFAULT_DIARY_SETTINGS = {
  backgroundColor: '#f5f5f5',
  isPrivate: false,
  title: '',
  content: ''
};

// Diary container CSS classes
export const DIARY_CONTAINER_CLASSES = {
  base: 'container border border-gray-300 rounded-lg shadow-lg bg-pink-100 p-2 mx-auto',
  withDecorating: 'mt-[350px]',
  withoutDecorating: 'mt-10 sm:mt-20',
  minHeight: 'min-h-[500px]'
};

// Common diary layout classes
export const DIARY_LAYOUT_CLASSES = {
  mainContainer: 'flex justify-center items-start min-h-[calc(100vh-180px)] p-6',
  diaryWrapper: 'relative min-h-[668px] transition-all duration-300',
  openWidth: 'w-full max-w-[1280px]',
  closedWidth: 'w-full max-w-[648px]',
  myDiaryOpenWidth: 'w-full max-w-[900px]',
  myDiaryClosedWidth: 'w-full max-w-[647px]'
};

// Loading states
export const LOADING_MESSAGES = {
  diary: 'Loading your diary...',
  entries: 'Loading diary entries...',
  settings: 'Loading diary settings...',
  saving: 'Saving your diary...'
};

// Validation error messages
export const VALIDATION_MESSAGES = {
  subjectRequired: 'Subject is required',
  subjectEmpty: 'Subject cannot be empty',
  contentRequired: 'Content is required',
  contentEmpty: 'Content cannot be empty'
};

// API endpoints
export const DIARY_ENDPOINTS = {
  entries: '/diary/entries',
  settings: '/diary/settings',
  search: '/diary/entries/search',
  sharedEntries: 'diary/shared-entries'
};

// Pagination defaults
export const PAGINATION_DEFAULTS = {
  itemsPerPage: 10,
  currentPage: 1
};
