'use client';

import { useEffect, useCallback, useState, useRef } from 'react';
import { format } from 'date-fns';
import { toast } from 'sonner';
import { useRouter } from 'next/navigation';
import { useDispatch, useSelector } from 'react-redux';

import api from '@/lib/api';
import DiaryForm from './_component/DiaryForm';
import DiarySkin from './_component/DiarySkin';
import SelectSkinModal from './_component/SelectSkinModal';
import MessageModal from './_component/modalContents/MessageModal';

// Shared utilities and hooks
import { useDiaryEntry } from './_hooks/useDiaryEntry';
import { useDiaryState } from './_hooks/useDiaryState';
import { useDiaryStage } from './_hooks/useDiaryStage';
import { useDiaryDecoration } from './_hooks/useDiaryDecoration';
import { DiaryConfigProvider } from './_config/diaryProvider';
import DiaryLayout from './_components/shared/DiaryLayout';
import { getCurrentEntryDate } from './_utils/diaryHelpers';

// Redux imports
import {
  setPreviewMode,
  updateCanvasItem,
  changeBackground,
  addImageToCanvas,
  resetCanvas,
  selectCanvasItems,
  selectCanvasBackground,
  setSelectedId,
} from '@/store/features/canvasSlice';

import {
  setIsTodayDiaryOpen,
  clearDecorations,
} from '@/store/features/diarySlice';

import { selectDiaryContainerProps } from './_store/diarySelectors';

function WriteDiaryContent() {
  const router = useRouter();
  const dispatch = useDispatch();
  const isInitialMount = useRef(true);
  
  // Use custom hooks for diary functionality
  const { date } = useSelector((state) => state.diary);
  const { user } = useSelector((state) => state.auth);
  const canvasItems = useSelector(selectCanvasItems);
  const containerProps = useSelector(selectDiaryContainerProps);
  
  // Diary state management
  const {
    subject,
    message,
    wordCount,
    isEdit,
    setIsEdit,
    selectedSkin,
    isSaving,
    isLoading,
    todayEntry,
    isSkinModalOpen,
    layoutBackground,
    handleSubjectChange,
    handleMessageChange,
    hasUnsavedChanges
  } = useDiaryState();

  // Diary entry operations
  const {
    diaryEntry,
    validationErrors,
    selectedStageTemplateId,
    setSelectedStageTemplateId,
    fetchDiaryEntry,
    saveDiaryEntry
  } = useDiaryEntry(null, date);

  // Stage management
  const {
    selectedStage,
    nextStage,
    handleStageChange,
    fetchDiarySettings
  } = useDiaryStage(selectedStageTemplateId);

  // Decoration management
  const {
    isDecorating,
    decorationItems,
    toggleDecorationMode,
    submitDecorations,
    clearAllDecorations
  } = useDiaryDecoration();

  // Local state
  const [hasTutorGreeting, setHasTutorGreeting] = useState(false);
  const [showSideIcons, setShowSideIcons] = useState(false);

  // Computed values
  const today = format(new Date(), 'dd MMM yyyy');
  const currentEntryDate = getCurrentEntryDate(diaryEntry, date);

  // Function to open diary
  const openDiary = useCallback(() => {
    dispatch(setIsTodayDiaryOpen(true));
  }, [dispatch]);

  // Function to close diary
  const closeDiary = useCallback(() => {
    dispatch(setIsTodayDiaryOpen(false));
  }, [dispatch]);

  // Handle responsive sidebar behavior
  useEffect(() => {
    const handleResize = () => {
      const isDesktop = window.innerWidth >= 1280; // xl breakpoint
      if (isDesktop) {
        setShowSideIcons(true); // Always show sidebar on desktop
      } else {
        setShowSideIcons(false); // Hide sidebar on mobile/tablet by default
      }
    };

    // Initial check
    handleResize();

    // Add event listener
    window.addEventListener('resize', handleResize);
    // Cleanup
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Initialize diary settings on mount
  useEffect(() => {
    if (isInitialMount.current) {
      fetchDiarySettings();
      isInitialMount.current = false;
    }
  }, [fetchDiarySettings]);

  // Handle save operation
  const handleSave = useCallback(async () => {
    const success = await saveDiaryEntry();
    if (success && nextStage) {
      // Auto-progress to next stage if available
      handleStageChange(nextStage);
    }
  }, [saveDiaryEntry, nextStage, handleStageChange]);

  // Handle decoration submission
  const handleSubmitDecoration = useCallback(async () => {
    if (diaryEntry?.id) {
      await submitDecorations(diaryEntry.id);
    }
  }, [submitDecorations, diaryEntry]);

  // Handle canvas operations
  const handleCanvasUpdate = useCallback((item) => {
    dispatch(updateCanvasItem(item));
  }, [dispatch]);

  const handleBackgroundChange = useCallback((background) => {
    dispatch(changeBackground(background));
  }, [dispatch]);

  const handleAddImage = useCallback((image) => {
    dispatch(addImageToCanvas(image));
  }, [dispatch]);

  // Render cover when diary is closed
  if (!containerProps.isOpen) {
    return (
      <div className="flex justify-center items-center min-h-[calc(100vh-180px)] p-6">
        <div 
          className="relative w-full max-w-[648px] min-h-[668px] bg-gradient-to-br from-pink-100 to-yellow-100 rounded-lg shadow-lg border border-gray-300 cursor-pointer transition-all duration-300 hover:shadow-xl"
          onClick={openDiary}
        >
          <div className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 bg-white/30 backdrop-blur-lg rounded-2xl shadow-xl px-12 py-6 text-center min-w-[200px] border border-white/40 ring-1 ring-black/5">
            <h1 className="text-2xl font-bold text-[#1E3A8A]">Today's Diary</h1>
            <p className="text-[#3B82F6] mt-1">Click to open</p>
          </div>
        </div>
      </div>
    );
  }

  // Render main diary content
  return (
    <DiaryLayout
      isOpen={containerProps.isOpen}
      isLoading={containerProps.isLoading}
      isDecorating={containerProps.isDecorating}
      onClose={closeDiary}
      showSideIcons={showSideIcons}
      onCloseSideIcons={() => setShowSideIcons(false)}
      variant="main"
    >
      <div className="grid grid-cols-1 md:grid-cols-2 gap-2 items-center relative">
        <DiarySkin
          today={currentEntryDate}
          todayEntry={diaryEntry}
          subject={subject}
          message={message}
          selectedSkin={selectedSkin}
          handleStageChange={handleStageChange}
          selectedStageTemplateId={selectedStageTemplateId}
          fetchTodayEntry={fetchDiaryEntry}
          onSubmitDecoration={handleSubmitDecoration}
          isDecorating={isDecorating}
        />

        <div className="bg-white h-full p-2 overflow-hidden shadow-xl">
          <DiaryForm
            today={currentEntryDate}
            todayEntry={diaryEntry}
            subject={subject}
            message={message}
            wordCount={wordCount}
            selectedStage={selectedStage}
            validationErrors={validationErrors}
            fetchTodayEntry={fetchDiaryEntry}
            nextStage={nextStage}
            handleStageChange={handleStageChange}
            isEditable={isEdit}
            setIsEdit={setIsEdit}
            isEdit={isEdit}
            handleSave={handleSave}
            isSaving={isSaving}
            setIsMessageModalOpen={setHasTutorGreeting}
          />
        </div>
      </div>

      {/* Modals */}
      <SelectSkinModal />
      <MessageModal
        isOpen={hasTutorGreeting}
        onClose={() => setHasTutorGreeting(false)}
      />
    </DiaryLayout>
  );
}

export default function WriteDiary() {
  return (
    <DiaryConfigProvider pageType="main">
      <WriteDiaryContent />
    </DiaryConfigProvider>
  );
}
